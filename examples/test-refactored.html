<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Test Refactored Label Editor</title>
  <style>
    body {
      font-family: system-ui, sans-serif;
      margin: 20px;
      background: #f5f5f5;
    }
    .test-container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .test-section {
      margin-bottom: 30px;
      padding: 15px;
      border: 1px solid #e0e0e0;
      border-radius: 5px;
    }
    .test-section h3 {
      margin-top: 0;
      color: #333;
    }
    .status {
      padding: 10px;
      border-radius: 4px;
      margin: 10px 0;
    }
    .status.success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .status.error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    .status.warning {
      background: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
    }
    button {
      background: #007bff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px;
    }
    button:hover {
      background: #0056b3;
    }
    button:disabled {
      background: #6c757d;
      cursor: not-allowed;
    }
    .test-results {
      font-family: monospace;
      background: #f8f9fa;
      padding: 10px;
      border-radius: 4px;
      white-space: pre-wrap;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h1>🧪 Refactored Label Editor Test Suite</h1>
    <p>This page tests the refactored Label Editor modules to ensure they work correctly.</p>
    
    <div class="test-section">
      <h3>📚 Library Loading Test</h3>
      <p>Testing if required libraries are loaded properly.</p>
      <button onclick="testLibraries()">Test Libraries</button>
      <div id="libraryStatus" class="status" style="display: none;"></div>
    </div>
    
    <div class="test-section">
      <h3>🎨 Canvas Initialization Test</h3>
      <p>Testing canvas creation and basic functionality.</p>
      <button onclick="testCanvas()">Test Canvas</button>
      <div id="canvasStatus" class="status" style="display: none;"></div>
      <canvas id="testCanvas" width="200" height="100" style="border: 1px solid #ccc; margin-top: 10px; display: none;"></canvas>
    </div>
    
    <div class="test-section">
      <h3>⚙️ Configuration Test</h3>
      <p>Testing configuration module and constants.</p>
      <button onclick="testConfig()">Test Config</button>
      <div id="configStatus" class="status" style="display: none;"></div>
    </div>
    
    <div class="test-section">
      <h3>🔧 Module Loading Test</h3>
      <p>Testing if all JavaScript modules load correctly.</p>
      <button onclick="testModules()">Test Modules</button>
      <div id="moduleStatus" class="status" style="display: none;"></div>
    </div>
    
    <div class="test-section">
      <h3>📊 Test Results</h3>
      <button onclick="runAllTests()">Run All Tests</button>
      <button onclick="clearResults()">Clear Results</button>
      <div id="testResults" class="test-results" style="display: none;"></div>
    </div>
    
    <div class="test-section">
      <h3>🔗 Quick Links</h3>
      <p>
        <a href="editor-refactored.html" target="_blank">Open Refactored Editor</a> |
        <a href="editor.html" target="_blank">Open Original Editor</a> |
        <a href="README-refactored.md" target="_blank">View Documentation</a>
      </p>
    </div>
  </div>

  <!-- Load libraries first -->
  <script src="./lib/fabric.min.js"></script>
  <script src="./lib/qrcode.min.js"></script>
  
  <script>
    let testResults = [];
    
    function log(message, type = 'info') {
      const timestamp = new Date().toLocaleTimeString();
      testResults.push(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
      updateTestResults();
    }
    
    function updateTestResults() {
      const resultsDiv = document.getElementById('testResults');
      resultsDiv.textContent = testResults.join('\n');
      resultsDiv.style.display = 'block';
    }
    
    function showStatus(elementId, message, type) {
      const element = document.getElementById(elementId);
      element.textContent = message;
      element.className = `status ${type}`;
      element.style.display = 'block';
    }
    
    function testLibraries() {
      log('Testing library loading...');
      
      let success = true;
      let messages = [];
      
      // Test Fabric.js
      if (typeof fabric !== 'undefined') {
        messages.push('✅ Fabric.js loaded successfully');
        log('Fabric.js version: ' + (fabric.version || 'unknown'));
      } else {
        messages.push('❌ Fabric.js not loaded');
        success = false;
      }
      
      // Test QRCode library
      if (typeof qrcode !== 'undefined') {
        messages.push('✅ QRCode library loaded successfully');
        log('QRCode library available');
      } else {
        messages.push('❌ QRCode library not loaded');
        success = false;
      }
      
      const status = success ? 'success' : 'error';
      showStatus('libraryStatus', messages.join('\n'), status);
      log(`Library test ${success ? 'PASSED' : 'FAILED'}`);
    }
    
    function testCanvas() {
      log('Testing canvas functionality...');
      
      try {
        if (typeof fabric === 'undefined') {
          throw new Error('Fabric.js not available');
        }
        
        const testCanvas = document.getElementById('testCanvas');
        testCanvas.style.display = 'block';
        
        const canvas = new fabric.Canvas('testCanvas');
        canvas.setBackgroundColor('#f0f0f0', canvas.renderAll.bind(canvas));
        
        // Add a simple rectangle
        const rect = new fabric.Rect({
          left: 10,
          top: 10,
          width: 50,
          height: 30,
          fill: '#007bff'
        });
        canvas.add(rect);
        
        showStatus('canvasStatus', '✅ Canvas test passed - Rectangle added successfully', 'success');
        log('Canvas test PASSED');
        
      } catch (error) {
        showStatus('canvasStatus', `❌ Canvas test failed: ${error.message}`, 'error');
        log(`Canvas test FAILED: ${error.message}`, 'error');
      }
    }
    
    async function testConfig() {
      log('Testing configuration module...');
      
      try {
        // Try to import config module
        const configModule = await import('./js/config.js');
        
        if (configModule.DPI && configModule.PAPER_SIZES) {
          const message = `✅ Config loaded - DPI: ${configModule.DPI}, Paper sizes: ${Object.keys(configModule.PAPER_SIZES).length}`;
          showStatus('configStatus', message, 'success');
          log('Config test PASSED');
        } else {
          throw new Error('Config module missing expected exports');
        }
        
      } catch (error) {
        showStatus('configStatus', `❌ Config test failed: ${error.message}`, 'error');
        log(`Config test FAILED: ${error.message}`, 'error');
      }
    }
    
    async function testModules() {
      log('Testing module loading...');
      
      const modules = [
        { name: 'config.js', path: './js/config.js' },
        { name: 'canvas-manager.js', path: './js/canvas-manager.js' },
        { name: 'element-creator.js', path: './js/element-creator.js' },
        { name: 'ui-controls.js', path: './js/ui-controls.js' },
        { name: 'data-manager.js', path: './js/data-manager.js' },
        { name: 'print-manager.js', path: './js/print-manager.js' },
        { name: 'app.js', path: './js/app.js' }
      ];
      
      let loadedCount = 0;
      let messages = [];
      
      for (const module of modules) {
        try {
          await import(module.path);
          messages.push(`✅ ${module.name}`);
          loadedCount++;
          log(`Module ${module.name} loaded successfully`);
        } catch (error) {
          messages.push(`❌ ${module.name}: ${error.message}`);
          log(`Module ${module.name} failed: ${error.message}`, 'error');
        }
      }
      
      const success = loadedCount === modules.length;
      const status = success ? 'success' : (loadedCount > 0 ? 'warning' : 'error');
      const summary = `${loadedCount}/${modules.length} modules loaded successfully`;
      
      showStatus('moduleStatus', `${summary}\n\n${messages.join('\n')}`, status);
      log(`Module test ${success ? 'PASSED' : 'PARTIALLY PASSED'} - ${summary}`);
    }
    
    async function runAllTests() {
      log('=== Starting Full Test Suite ===');
      clearResults();
      
      testLibraries();
      await new Promise(resolve => setTimeout(resolve, 500));
      
      testCanvas();
      await new Promise(resolve => setTimeout(resolve, 500));
      
      await testConfig();
      await new Promise(resolve => setTimeout(resolve, 500));
      
      await testModules();
      
      log('=== Test Suite Complete ===');
    }
    
    function clearResults() {
      testResults = [];
      document.getElementById('testResults').style.display = 'none';
      
      // Hide all status messages
      ['libraryStatus', 'canvasStatus', 'configStatus', 'moduleStatus'].forEach(id => {
        document.getElementById(id).style.display = 'none';
      });
      
      // Hide test canvas
      document.getElementById('testCanvas').style.display = 'none';
    }
    
    // Auto-run basic tests on page load
    window.addEventListener('load', () => {
      log('Test page loaded');
      setTimeout(() => {
        testLibraries();
      }, 1000);
    });
  </script>
</body>
</html>
