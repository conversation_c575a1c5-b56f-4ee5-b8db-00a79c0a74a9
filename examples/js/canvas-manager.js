/**
 * Canvas Management Module
 * Handles canvas initialization, sizing, orientation, and basic operations
 */

import { 
  DPI, 
  DEFAULT_WIDTH_MM, 
  DEFAULT_HEIGHT_MM, 
  PAPER_SIZES, 
  MAX_DISPLAY_WIDTH,
  mmToDots 
} from './config.js';

// Canvas state
let stage = null;
let WIDTH_DOTS = mmToDots(DEFAULT_WIDTH_MM);
let HEIGHT_DOTS = mmToDots(DEFAULT_HEIGHT_MM);
let CURRENT_WIDTH_MM = DEFAULT_WIDTH_MM;
let CURRENT_HEIGHT_MM = DEFAULT_HEIGHT_MM;
let CURRENT_ORIENTATION = 'portrait'; // 'portrait' or 'landscape'

// History for undo functionality
let history = [];
let historyIndex = -1;
const MAX_HISTORY = 50;

/**
 * Initialize the Fabric.js canvas
 */
export function initializeCanvas() {
  stage = new fabric.Canvas('stage', {
    backgroundColor: '#fff',
    selection: true,
    preserveObjectStacking: true,
    enableRetinaScaling: true,
  });
  
  stage.setWidth(WIDTH_DOTS);
  stage.setHeight(HEIGHT_DOTS);
  
  // Set up canvas event listeners
  setupCanvasEvents();
  
  // Initialize history
  snapshot();
  
  return stage;
}

/**
 * Get the current canvas instance
 */
export function getCanvas() {
  return stage;
}

/**
 * Set up canvas event listeners
 */
function setupCanvasEvents() {
  if (!stage) return;
  
  // Object selection events
  stage.on('selection:created', updateActionButtons);
  stage.on('selection:updated', updateActionButtons);
  stage.on('selection:cleared', updateActionButtons);
  
  // Object modification events
  stage.on('object:modified', () => {
    snapshot();
    updateActionButtons();
  });
  
  // Object added/removed events
  stage.on('object:added', updateActionButtons);
  stage.on('object:removed', updateActionButtons);
}

/**
 * Update canvas size based on paper dimensions
 */
export function updateCanvasSize(widthMm, heightMm) {
  if (!stage) return;
  
  // Check if there are objects on the canvas
  if (stage.getObjects().length > 0) {
    const confirmed = confirm(
      'Changing paper size will clear all elements on the label. Do you want to continue?'
    );
    if (!confirmed) {
      revertPaperSizeSelection();
      return;
    }
    clearCanvas();
  }

  CURRENT_WIDTH_MM = widthMm;
  CURRENT_HEIGHT_MM = heightMm;

  // Update canvas size based on current orientation
  updateCanvasSizeForOrientation();
  updateOrientationInfo();
  snapshot();
}

/**
 * Handle paper size selection change
 */
export function handlePaperSizeChange() {
  const select = document.getElementById('paperSize');
  const customInputs = document.getElementById('customSizeInputs');

  if (select.value === 'custom') {
    customInputs.classList.remove('hidden');
  } else {
    customInputs.classList.add('hidden');
    const size = PAPER_SIZES[select.value];
    if (size) {
      updateCanvasSize(size.width, size.height);
    }
  }
}

/**
 * Apply custom paper size
 */
export function applyCustomSize() {
  const width = parseInt(document.getElementById('customWidth').value) || 60;
  const height = parseInt(document.getElementById('customHeight').value) || 20;

  // Validate size limits
  const minSize = 10, maxSize = 200;
  const validWidth = Math.max(minSize, Math.min(maxSize, width));
  const validHeight = Math.max(minSize, Math.min(maxSize, height));

  // Update input values if they were corrected
  document.getElementById('customWidth').value = validWidth;
  document.getElementById('customHeight').value = validHeight;

  updateCanvasSize(validWidth, validHeight);
}

/**
 * Set canvas orientation
 */
export function setOrientation(orientation) {
  if (!stage) return;
  
  // Don't change if already the same orientation
  if (CURRENT_ORIENTATION === orientation) return;

  // Check if there are objects on the canvas
  if (stage.getObjects().length > 0) {
    const confirmed = confirm(
      'Changing orientation will clear all elements on the label. Do you want to continue?'
    );
    if (!confirmed) {
      return;
    }
    clearCanvas();
  }

  CURRENT_ORIENTATION = orientation;

  // Update button states
  updateOrientationButtons();
  updateCanvasSizeForOrientation();
  updateOrientationInfo();
  snapshot();
}

/**
 * Update orientation button states
 */
function updateOrientationButtons() {
  const portraitBtn = document.getElementById('orientationPortrait');
  const landscapeBtn = document.getElementById('orientationLandscape');
  
  portraitBtn.classList.remove('bg-blue-500', 'text-white');
  landscapeBtn.classList.remove('bg-blue-500', 'text-white');

  if (CURRENT_ORIENTATION === 'portrait') {
    portraitBtn.classList.add('bg-blue-500', 'text-white');
  } else {
    landscapeBtn.classList.add('bg-blue-500', 'text-white');
  }
}

/**
 * Update canvas size based on orientation
 */
function updateCanvasSizeForOrientation() {
  if (!stage) return;
  
  const stageWrap = document.getElementById('stageWrap');

  if (CURRENT_ORIENTATION === 'landscape') {
    // For landscape, swap the display dimensions
    const tempWidth = CURRENT_WIDTH_MM;
    const tempHeight = CURRENT_HEIGHT_MM;

    WIDTH_DOTS = mmToDots(tempHeight);
    HEIGHT_DOTS = mmToDots(tempWidth);

    stage.setWidth(WIDTH_DOTS);
    stage.setHeight(HEIGHT_DOTS);

    // Calculate display size
    const aspectRatio = HEIGHT_DOTS / WIDTH_DOTS;
    let displayWidth = Math.min(WIDTH_DOTS, MAX_DISPLAY_WIDTH);
    let displayHeight = displayWidth * aspectRatio;

    applyCanvasDisplaySize(displayWidth, displayHeight);
    stageWrap.style.width = displayWidth + 'px';
    stageWrap.style.height = displayHeight + 'px';

    // Update title
    document.querySelector('h1').textContent = `Label Editor (${tempHeight}×${tempWidth} mm @203dpi)`;
  } else {
    // For portrait, use normal dimensions
    WIDTH_DOTS = mmToDots(CURRENT_WIDTH_MM);
    HEIGHT_DOTS = mmToDots(CURRENT_HEIGHT_MM);

    stage.setWidth(WIDTH_DOTS);
    stage.setHeight(HEIGHT_DOTS);

    // Calculate display size
    const aspectRatio = HEIGHT_DOTS / WIDTH_DOTS;
    let displayWidth = Math.min(WIDTH_DOTS, MAX_DISPLAY_WIDTH);
    let displayHeight = displayWidth * aspectRatio;

    applyCanvasDisplaySize(displayWidth, displayHeight);
    stageWrap.style.width = displayWidth + 'px';
    stageWrap.style.height = displayHeight + 'px';

    // Update title
    document.querySelector('h1').textContent = `Label Editor (${CURRENT_WIDTH_MM}×${CURRENT_HEIGHT_MM} mm @203dpi)`;
  }

  // Force Fabric.js to recalculate
  setTimeout(() => {
    stage.calcOffset();
    stage.requestRenderAll();
  }, 10);
}

/**
 * Apply display size to canvas elements
 */
function applyCanvasDisplaySize(displayWidth, displayHeight) {
  if (!stage) return;
  
  // Store display size
  stage._displayWidth = displayWidth;
  stage._displayHeight = displayHeight;

  // Apply to all canvas elements
  const canvasElements = document.querySelectorAll('#stageWrap canvas');
  canvasElements.forEach(canvas => {
    canvas.style.width = displayWidth + 'px';
    canvas.style.height = displayHeight + 'px';
    canvas.style.maxWidth = '500px';
    canvas.style.display = 'block';
  });

  // Apply to main canvas
  const mainCanvas = document.getElementById('stage');
  if (mainCanvas) {
    mainCanvas.style.width = displayWidth + 'px';
    mainCanvas.style.height = displayHeight + 'px';
    mainCanvas.style.maxWidth = '500px';
  }

  stage.calcOffset();
  stage.requestRenderAll();
}

/**
 * Update orientation info display
 */
function updateOrientationInfo() {
  const info = document.getElementById('orientationInfo');
  if (!info) return;
  
  if (CURRENT_ORIENTATION === 'portrait') {
    info.textContent = `Portrait: ${CURRENT_WIDTH_MM}×${CURRENT_HEIGHT_MM} mm (W×H)`;
  } else {
    info.textContent = `Landscape: ${CURRENT_HEIGHT_MM}×${CURRENT_WIDTH_MM} mm (W×H) - Display rotated`;
  }
}

/**
 * Get actual paper size (not swapped)
 */
export function getActualPaperSize() {
  return { width: CURRENT_WIDTH_MM, height: CURRENT_HEIGHT_MM };
}

/**
 * Get current canvas dimensions in dots
 */
export function getCanvasDimensions() {
  return { width: WIDTH_DOTS, height: HEIGHT_DOTS };
}

/**
 * Get current orientation
 */
export function getCurrentOrientation() {
  return CURRENT_ORIENTATION;
}

/**
 * Clear all objects from canvas
 */
export function clearCanvas() {
  if (!stage) return;
  
  stage.getObjects().slice().forEach((o) => stage.remove(o));
  stage.discardActiveObject();
  stage.requestRenderAll();
  updateActionButtons();
  snapshot();
}

/**
 * Revert paper size selection to current size
 */
function revertPaperSizeSelection() {
  const paperSizeSelect = document.getElementById('paperSize');
  const customInputs = document.getElementById('customSizeInputs');

  // Check if current size matches any standard size
  let matchedStandardSize = null;
  for (const [sizeKey, sizeValue] of Object.entries(PAPER_SIZES)) {
    if (sizeValue.width === CURRENT_WIDTH_MM && sizeValue.height === CURRENT_HEIGHT_MM) {
      matchedStandardSize = sizeKey;
      break;
    }
  }

  if (matchedStandardSize) {
    paperSizeSelect.value = matchedStandardSize;
    customInputs.classList.add('hidden');
  } else {
    paperSizeSelect.value = 'custom';
    customInputs.classList.remove('hidden');
    document.getElementById('customWidth').value = CURRENT_WIDTH_MM;
    document.getElementById('customHeight').value = CURRENT_HEIGHT_MM;
  }
}

/**
 * Take a snapshot for undo functionality
 */
export function snapshot() {
  if (!stage) return;
  
  const state = JSON.stringify(stage.toJSON());
  
  // Remove any states after current index
  history = history.slice(0, historyIndex + 1);
  
  // Add new state
  history.push(state);
  
  // Limit history size
  if (history.length > MAX_HISTORY) {
    history = history.slice(-MAX_HISTORY);
  }
  
  historyIndex = history.length - 1;
}

/**
 * Undo last action
 */
export function undo() {
  if (!stage || historyIndex <= 0) return;
  
  historyIndex--;
  const state = history[historyIndex];
  
  stage.loadFromJSON(state, () => {
    stage.requestRenderAll();
    updateActionButtons();
  });
}

/**
 * Update action button states (placeholder - will be implemented in ui-controls.js)
 */
function updateActionButtons() {
  // This will be implemented in ui-controls.js
  if (window.updateActionButtons) {
    window.updateActionButtons();
  }
}

// Export canvas state getters
export { WIDTH_DOTS, HEIGHT_DOTS, CURRENT_WIDTH_MM, CURRENT_HEIGHT_MM, CURRENT_ORIENTATION };
