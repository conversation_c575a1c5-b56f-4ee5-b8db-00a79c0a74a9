<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Label Editor (Standalone - No Modules)</title>
  
  <!-- External Libraries -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@400;600;700&family=Noto+Sans+Thai:wght@400;600&display=swap" rel="stylesheet" />
  <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
  
  <!-- Application Styles -->
  <link rel="stylesheet" href="./css/styles.css" />
  
  <!-- Local Libraries -->
  <script src="./lib/fabric.min.js"></script>
  <script src="./lib/qrcode.min.js"></script>
</head>
<body>
  <div class="max-w-7xl mx-auto p-4">
    <h1 class="text-xl font-semibold mb-4">
      Label Editor (Standalone Test - 60×20 mm @203dpi)
    </h1>
    
    <div class="flex flex-col lg:flex-row gap-4">
      <!-- Left Panel: Controls -->
      <div class="flex-1 flex flex-col gap-2">
        
        <!-- Print Settings Section -->
        <div class="bg-white border rounded p-3">
          <h3 class="font-medium mb-2">Print Settings</h3>
          <div class="flex flex-col gap-2">
            <label class="text-sm text-slate-600">Printer</label>
            <input id="printer" class="hidden" />
            <div class="border rounded p-2 bg-slate-50">
              <div class="flex items-center justify-between mb-2">
                <div class="text-sm text-slate-600">Select a printer</div>
                <button id="refreshPrinters" class="text-xs px-2 py-1 bg-slate-200 rounded hover:bg-slate-300">
                  Refresh
                </button>
              </div>
              <div id="printerList" class="flex flex-col gap-1 max-h-44 overflow-auto">
                <div class="text-sm text-slate-500">Click Refresh to load printers</div>
              </div>
            </div>
            
            <div class="mt-2 flex items-center gap-3 flex-wrap">
              <button id="print" class="px-5 py-3 bg-indigo-600 text-white rounded hover:bg-indigo-500 text-base font-semibold shadow inline-flex items-center gap-2" disabled>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                  <path d="M6 9V2h12v7H6zm10-2V4H8v3h8zM6 14H4a2 2 0 01-2-2V9a2 2 0 012-2h16a2 2 0 012 2v3a2 2 0 01-2 2h-2v5H6v-5zm2 0v3h8v-3H8z" />
                </svg>
                Print
              </button>
              
              <span id="status" class="text-sm text-slate-600 ml-1">Ready</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Panel: Canvas -->
      <div class="flex-1 flex flex-col gap-4">
        <!-- Canvas Area -->
        <div class="bg-white border rounded p-3 flex justify-center items-center">
          <div id="stageWrap" class="inline-block border rounded bg-white">
            <canvas id="stage" width="480" height="160"></canvas>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Global variables
    let stage = null;
    let availablePrinters = [];
    let selectedPrinter = null;

    // Initialize application
    function initApp() {
      console.log('🚀 Initializing Standalone Label Editor...');
      
      // Check libraries
      if (typeof fabric === 'undefined') {
        alert('Fabric.js library not loaded');
        return;
      }
      
      // Initialize canvas
      stage = new fabric.Canvas('stage', {
        backgroundColor: '#fff',
        selection: true,
        preserveObjectStacking: true,
        enableRetinaScaling: true,
      });
      
      // Setup event listeners
      setupEventListeners();
      
      // Load printers
      refreshPrinters();
      
      console.log('✅ Standalone Label Editor initialized');
    }
    
    function setupEventListeners() {
      // Refresh printers button
      document.getElementById('refreshPrinters').addEventListener('click', refreshPrinters);
      
      // Print button
      document.getElementById('print').addEventListener('click', handlePrint);
    }
    
    async function refreshPrinters() {
      console.log('🖨️ Refreshing printers...');
      
      const printerList = document.getElementById('printerList');
      const statusSpan = document.getElementById('status');
      
      if (!printerList) return;
      
      // Show loading state
      printerList.innerHTML = '<div class="text-sm text-slate-500">Loading…</div>';
      statusSpan.textContent = 'Loading printers...';
      
      try {
        // Try to get printers from local API
        const response = await fetch('http://localhost:7788/printers/status', {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          },
          mode: 'cors'
        });
        
        console.log(`API Response status: ${response.status}`);
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log('API Data:', data);
        
        availablePrinters = (data.printers || []).map(printer => ({
          id: printer.name || printer.id,
          name: printer.name || printer.id,
          type: 'label',
          status: printer.state || printer.status || 'unknown',
          ready: printer.state === 'idle' || printer.ready === true,
          isDefault: printer.isDefault || false
        }));
        
        console.log(`Processed ${availablePrinters.length} printers:`, availablePrinters);
        
        renderPrinterList();
        
        // Store printer state globally (for compatibility)
        window.__printersState = {};
        availablePrinters.forEach(printer => {
          window.__printersState[printer.id] = printer;
        });
        
        statusSpan.textContent = `Found ${availablePrinters.length} printer(s)`;
        
      } catch (error) {
        console.error('Failed to refresh printers:', error);
        
        let errorMessage = 'Failed to load printers';
        if (error.message.includes('fetch') || error.message.includes('NetworkError')) {
          errorMessage = 'Cannot connect to printer service (localhost:7788)';
        } else if (error.message.includes('CORS')) {
          errorMessage = 'CORS error - check printer service configuration';
        }
        
        printerList.innerHTML = `<div class="text-sm text-rose-600">${errorMessage}</div>`;
        statusSpan.textContent = errorMessage;
        
        // Show fallback printers for testing
        availablePrinters = [
          { id: 'default', name: 'Default Printer', type: 'system', status: 'idle', ready: true, isDefault: true },
          { id: 'pdf', name: 'Save as PDF', type: 'pdf', status: 'idle', ready: true, isDefault: false }
        ];
        renderPrinterList();
      }
    }
    
    function renderPrinterList() {
      const printerList = document.getElementById('printerList');
      if (!printerList) return;
      
      printerList.innerHTML = '';
      
      if (availablePrinters.length === 0) {
        printerList.innerHTML = '<div class="text-sm text-slate-500">No printers found</div>';
        return;
      }
      
      const current = (document.getElementById('printer')?.value || '').trim();
      
      availablePrinters.forEach(printer => {
        const row = document.createElement('button');
        row.type = 'button';
        row.className = 'flex items-center justify-between w-full px-2 py-1 rounded hover:bg-green-300 text-left';
        
        const left = document.createElement('div');
        left.className = 'flex items-center gap-2';
        
        // Status indicator
        let color = 'bg-slate-400';
        if (printer.status === 'idle') color = 'bg-green-500';
        else if (printer.status === 'printing') color = 'bg-amber-500';
        else if (printer.status === 'offline' || printer.status === 'disabled') color = 'bg-rose-500';
        
        const dot = document.createElement('span');
        dot.className = 'inline-block w-2 h-2 rounded-full ' + color;
        
        const name = document.createElement('span');
        name.textContent = printer.name + (printer.isDefault ? ' (default)' : '');
        
        left.append(dot, name);
        
        const right = document.createElement('span');
        right.className = 'text-xs text-slate-500';
        right.textContent = printer.status || 'unknown';
        
        row.append(left, right);
        
        // Highlight current selection
        if (printer.name === current) {
          row.classList.add('bg-green-300');
        }
        
        // Click handler
        row.onclick = () => {
          document.getElementById('printer').value = printer.name;
          [...printerList.children].forEach(ch => ch.classList.remove('bg-green-300'));
          row.classList.add('bg-green-300');
          selectedPrinter = printer;
          updatePrintButtonState();
          
          console.log(`Selected printer: ${printer.name}`);
        };
        
        printerList.append(row);
      });
      
      // Auto-select preferred printer if none selected
      if (!current && availablePrinters.length > 0) {
        const preferred = 
          availablePrinters.find(p => p.name === 'AiYin_A70pro') ||
          availablePrinters.find(p => /aiyin|a70/i.test(p.name)) ||
          availablePrinters.find(p => p.isDefault) ||
          availablePrinters[0];
          
        if (preferred) {
          document.getElementById('printer').value = preferred.name;
          selectedPrinter = preferred;
          
          // Highlight the selected printer
          [...printerList.children].forEach(ch => {
            if (ch.textContent && ch.textContent.startsWith(preferred.name)) {
              ch.classList.add('bg-green-300');
            }
          });
          
          console.log(`Auto-selected printer: ${preferred.name}`);
        }
      }
      
      updatePrintButtonState();
    }
    
    function updatePrintButtonState() {
      const printBtn = document.getElementById('print');
      const statusSpan = document.getElementById('status');
      
      if (!printBtn) return;
      
      const hasSelectedPrinter = !!selectedPrinter;
      const printerReady = selectedPrinter?.ready !== false;
      
      if (hasSelectedPrinter && printerReady) {
        printBtn.disabled = false;
        printBtn.className = 'px-5 py-3 bg-indigo-600 text-white rounded hover:bg-indigo-500 text-base font-semibold shadow inline-flex items-center gap-2';
        
        if (statusSpan && selectedPrinter) {
          statusSpan.textContent = `Ready: ${selectedPrinter.name}`;
        }
      } else {
        printBtn.disabled = true;
        printBtn.className = 'px-5 py-3 bg-slate-300 text-slate-500 rounded text-base font-semibold shadow inline-flex items-center gap-2 cursor-not-allowed';
        
        if (statusSpan) {
          if (!hasSelectedPrinter) {
            statusSpan.textContent = 'No printer selected';
          } else {
            statusSpan.textContent = `Printer not ready: ${selectedPrinter.name}`;
          }
        }
      }
    }
    
    function handlePrint() {
      if (!selectedPrinter) {
        alert('Please select a printer first');
        return;
      }
      
      console.log('Print button clicked, selected printer:', selectedPrinter);
      alert(`Print job sent to: ${selectedPrinter.name}\n(This is a test - no actual printing)`);
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initApp);
    } else {
      initApp();
    }
  </script>
</body>
</html>
