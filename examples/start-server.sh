#!/bin/bash

# Start HTTP Server for Label Editor
# This script starts a simple HTTP server to serve the refactored Label Editor

echo "🚀 Starting HTTP Server for Label Editor..."
echo ""
echo "📁 Serving files from: $(pwd)"
echo "🌐 Server will be available at:"
echo "   - http://localhost:8080/editor-refactored.html (Refactored version)"
echo "   - http://localhost:8080/editor-standalone.html (Standalone version)"
echo "   - http://localhost:8080/editor.html (Original version)"
echo ""
echo "🛑 Press Ctrl+C to stop the server"
echo ""

# Check if Python 3 is available
if command -v python3 &> /dev/null; then
    echo "Using Python 3..."
    python3 -m http.server 8080
elif command -v python &> /dev/null; then
    echo "Using Python 2..."
    python -m SimpleHTTPServer 8080
elif command -v node &> /dev/null; then
    echo "Using Node.js..."
    npx http-server -p 8080
else
    echo "❌ Error: No suitable HTTP server found!"
    echo "Please install Python 3, Python 2, or Node.js to run this server."
    exit 1
fi
