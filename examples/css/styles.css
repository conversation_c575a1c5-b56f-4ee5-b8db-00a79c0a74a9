/* ===== LABEL EDITOR STYLES ===== */

/* Base Styles */
body {
  font-family: system-ui, -apple-system, Segoe UI, Roboto, sans-serif;
  margin: 16px;
}

/* Layout Components */
.row {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 8px;
}

/* Form Elements */
label {
  min-width: 110px;
}

input,
select,
button {
  padding: 6px 8px;
}

/* Canvas Wrapper */
#stageWrap {
  border: 1px solid #ccc;
  display: inline-block;
  background: #fff;
  max-width: 500px; /* Fixed maximum width */
  overflow: hidden; /* Hide any overflow */
}

#stageWrap canvas {
  max-width: 500px !important;
  height: auto !important;
  display: block !important;
}

#stage {
  background: #fff;
}

/* Status Text */
#status {
  margin-left: 8px;
  color: #333;
}

/* Button States */
button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

button:not(:disabled):hover {
  opacity: 0.9;
}

/* Active States for Toggle Buttons */
.tt-font.active,
.style-btn.active,
.align-btn.active {
  background-color: #3b82f6 !important;
  color: white !important;
}

/* Font Style Buttons */
.tt-font {
  transition: all 0.2s ease;
}

.style-btn {
  transition: all 0.2s ease;
}

.align-btn {
  transition: all 0.2s ease;
}

/* Shape Control Buttons */
.shape-control-btn {
  transition: all 0.2s ease;
}

.shape-control-btn.active {
  background-color: #3b82f6 !important;
  color: white !important;
}

/* Template Cards */
.template-card {
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  padding: 0.75rem;
  background: white;
  transition: all 0.2s ease;
}

.template-card:hover {
  border-color: #cbd5e1;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.template-card.active {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

/* Printer List Items */
.printer-item {
  padding: 0.5rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.printer-item:hover {
  background-color: #f1f5f9;
}

.printer-item.selected {
  background-color: #dbeafe;
  border-color: #3b82f6;
}

/* Binding List Items */
.binding-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.25rem;
  background: #f8fafc;
}

.binding-item label {
  min-width: auto;
  font-size: 0.875rem;
  color: #475569;
}

.binding-item input,
.binding-item select {
  flex: 1;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

/* Custom Size Inputs */
#customSizeInputs {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Orientation Buttons */
.orientation-btn {
  transition: all 0.2s ease;
}

.orientation-btn.active {
  background-color: #3b82f6 !important;
  color: white !important;
}

/* Control Panels */
.control-panel {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
}

.control-panel h3 {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

/* Settings Panels (initially hidden) */
#textControls,
#shapeControls,
#imgControls,
#dateTimeControls,
#counterControls {
  display: none;
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Data JSON Textarea */
#dataJSON {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  resize: vertical;
  min-height: 120px;
}

/* Bindings List */
#bindingsList {
  max-height: 14rem;
  overflow-y: auto;
}

#bindingsList::-webkit-scrollbar {
  width: 6px;
}

#bindingsList::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

#bindingsList::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

#bindingsList::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Templates Grid */
#templates {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 0.75rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .max-w-7xl {
    padding: 1rem;
  }
  
  .flex-col.lg\\:flex-row {
    flex-direction: column;
  }
  
  #templates {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  body {
    margin: 8px;
  }
  
  .flex-wrap {
    flex-direction: column;
    align-items: stretch;
  }
  
  .inline-flex {
    width: 100%;
    justify-content: center;
  }
  
  #stageWrap {
    max-width: 100%;
    overflow-x: auto;
  }
  
  .control-panel {
    padding: 0.5rem;
  }
}

/* Print Button Special Styling */
#print {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

#print:hover {
  background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%);
  box-shadow: 0 6px 8px -1px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

#print:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

/* Save Template Button */
#saveTpl {
  transition: all 0.2s ease;
}

#saveTpl:hover {
  background-color: #e2e8f0;
  transform: translateY(-1px);
}

/* Clear Canvas Button */
#clearCanvas {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  transition: all 0.2s ease;
}

#clearCanvas:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-1px);
}

/* Export/Import Buttons */
#exportTemplates {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  transition: all 0.2s ease;
}

#exportTemplates:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-1px);
}

/* Utility Classes */
.hidden {
  display: none !important;
}

.italic {
  font-style: italic;
}

.underline {
  text-decoration: underline;
}

.line-through {
  text-decoration: line-through;
}

/* Focus States */
input:focus,
select:focus,
textarea:focus,
button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Loading States */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #3b82f6;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
