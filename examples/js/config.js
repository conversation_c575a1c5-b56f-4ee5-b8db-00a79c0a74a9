/**
 * Configuration constants and settings for the Label Editor
 */

// Print settings
export const DPI = 203;

// Default canvas dimensions (60x20mm at 203 DPI)
export const DEFAULT_WIDTH_MM = 60;
export const DEFAULT_HEIGHT_MM = 20;

// Paper size configurations
export const PAPER_SIZES = {
  '60x20': { width: 60, height: 20 },
  '50x20': { width: 50, height: 20 },
  '50x30': { width: 50, height: 30 },
  '40x30': { width: 40, height: 30 },
  '75x130': { width: 75, height: 130 }
};

// Canvas display settings
export const MAX_DISPLAY_WIDTH = 500; // Fixed maximum display width

// Default font settings
export const DEFAULT_FONT_FAMILY = 'Sarabun';
export const DEFAULT_FONT_SIZE = 24;
export const DEFAULT_FONT_WEIGHT = 600;
export const DEFAULT_TEXT_ALIGN = 'left';

// Font families available
export const FONT_FAMILIES = {
  'Sarabun': 'Sarabun, Noto Sans Thai, sans-serif',
  'Noto Sans Thai': 'Noto Sans Thai, sans-serif',
  'system': 'sans-serif'
};

// Font size limits
export const FONT_SIZE_MIN = 8;
export const FONT_SIZE_MAX = 200;
export const FONT_SIZE_STEP = 2;

// Font weight options
export const FONT_WEIGHTS = {
  normal: 400,
  medium: 500,
  semibold: 600,
  bold: 700
};

// Text alignment options
export const TEXT_ALIGNMENTS = ['left', 'center', 'right'];

// Shape styling options
export const LINE_STYLES = {
  'solid': null,
  'dashed': [8, 4],
  'dotted': [2, 4],
  'dashDot': [12, 4, 2, 4]
};

export const LINE_THICKNESSES = [1, 2, 3, 4, 5];

// Image processing settings
export const IMAGE_THRESHOLD_DEFAULT = 180;
export const IMAGE_THRESHOLD_MIN = 0;
export const IMAGE_THRESHOLD_MAX = 255;

// Date/Time format options
export const DATE_FORMATS = {
  'DD/MM/YYYY': 'DD/MM/YYYY',
  'DD/MM/YY': 'DD/MM/YY',
  'YYYY-MM-DD': 'YYYY-MM-DD',
  'dddd DD mmmm YYYY': 'dddd DD mmmm YYYY',
  'dd DD/mmm/YYYY': 'dd DD/mmm/YYYY',
  'mmmm DD, YYYY': 'mmmm DD, YYYY',
  'DD-MM-YYYY': 'DD-MM-YYYY',
  'DD/MM/YYYY HH:mm:ss': 'DD/MM/YYYY HH:mm:ss',
  'YYYY-MM-DD HH:mm:ss': 'YYYY-MM-DD HH:mm:ss'
};

export const TIME_FORMATS = {
  'HH:mm:ss': 'HH:mm:ss',
  'HH:mm': 'HH:mm',
  'hh:mm:ss AM/PM': 'hh:mm:ss AM/PM',
  'hh:mm AM/PM': 'hh:mm AM/PM'
};

// Date/Time localization
export const THAI_MONTHS_FULL = [
  'มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน',
  'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'
];

export const THAI_MONTHS_SHORT = [
  'ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.',
  'ก.ค.', 'ส.ค.', 'ก.ย.', 'ต.ค.', 'พ.ย.', 'ธ.ค.'
];

export const THAI_DAYS_FULL = [
  'อาทิตย์', 'จันทร์', 'อังคาร', 'พุธ', 'พฤหัสบดี', 'ศุกร์', 'เสาร์'
];

export const THAI_DAYS_SHORT = [
  'อา.', 'จ.', 'อ.', 'พ.', 'พฤ.', 'ศ.', 'ส.'
];

export const ENGLISH_MONTHS_FULL = [
  'January', 'February', 'March', 'April', 'May', 'June',
  'July', 'August', 'September', 'October', 'November', 'December'
];

export const ENGLISH_MONTHS_SHORT = [
  'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
  'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
];

export const ENGLISH_DAYS_FULL = [
  'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'
];

export const ENGLISH_DAYS_SHORT = [
  'Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'
];

// Counter settings
export const COUNTER_DEFAULT_START = 1;
export const COUNTER_DEFAULT_PADDING = 3;
export const COUNTER_MIN_VALUE = 0;
export const COUNTER_MAX_VALUE = 99999;
export const COUNTER_PADDING_OPTIONS = [1, 2, 3, 4, 5];

// Print settings
export const PRINT_DIRECTIONS = {
  0: '0 (front)',
  1: '1 (flip 180°)'
};

export const COLLATION_OPTIONS = {
  'collated': 'Collated',
  'uncollated': 'Uncollated'
};

// Template storage
export const TEMPLATE_STORAGE_KEY = 'labelTemplates';

// Data file candidates for auto-loading
export const DATA_FILE_CANDIDATES = ['data.json', '../data.json', '/data.json'];

// Sample data for demonstration
export const SAMPLE_DATA = [
  { "uic": "XX010101", "id_card": "1234123412340" },
  { "uic": "YY010101", "id_card": "1234123412341" }
];

// Barcode settings
export const BARCODE_DEFAULT_WIDTH = 2;
export const BARCODE_DEFAULT_HEIGHT = 100;
export const BARCODE_DEFAULT_MARGIN = 10;

// QR Code settings
export const QR_DEFAULT_SIZE = 100;
export const QR_ERROR_CORRECTION = 'M'; // L, M, Q, H

// Shape default settings
export const SHAPE_DEFAULTS = {
  line: {
    stroke: '#000',
    strokeWidth: 2,
    strokeDashArray: null
  },
  rect: {
    fill: 'transparent',
    stroke: '#000',
    strokeWidth: 2,
    strokeDashArray: null
  },
  circle: {
    fill: 'transparent',
    stroke: '#000',
    strokeWidth: 2,
    strokeDashArray: null
  },
  triangle: {
    fill: 'transparent',
    stroke: '#000',
    strokeWidth: 2,
    strokeDashArray: null
  }
};

// Element positioning
export const ELEMENT_DEFAULT_POSITION = {
  left: 5,
  top: 5
};

// Undo/Redo settings
export const MAX_HISTORY_SIZE = 50;

// File size limits
export const MAX_IMAGE_SIZE = 5 * 1024 * 1024; // 5MB
export const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp'];

// Utility functions
export function mmToDots(mm) {
  return Math.round(mm * DPI / 25.4);
}

export function dotsToMm(dots) {
  return Math.round(dots * 25.4 / DPI * 100) / 100;
}

export function validateImageFile(file) {
  if (!file) return { valid: false, error: 'No file selected' };
  
  if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {
    return { valid: false, error: 'Invalid file type. Please select a JPEG, PNG, GIF, or BMP image.' };
  }
  
  if (file.size > MAX_IMAGE_SIZE) {
    return { valid: false, error: 'File too large. Please select an image smaller than 5MB.' };
  }
  
  return { valid: true };
}

export function formatNumber(num, padding) {
  return String(num).padStart(padding, '0');
}

export function clamp(value, min, max) {
  return Math.min(Math.max(value, min), max);
}

// Export all as default for convenience
export default {
  DPI,
  DEFAULT_WIDTH_MM,
  DEFAULT_HEIGHT_MM,
  PAPER_SIZES,
  MAX_DISPLAY_WIDTH,
  DEFAULT_FONT_FAMILY,
  DEFAULT_FONT_SIZE,
  DEFAULT_FONT_WEIGHT,
  DEFAULT_TEXT_ALIGN,
  FONT_FAMILIES,
  FONT_SIZE_MIN,
  FONT_SIZE_MAX,
  FONT_SIZE_STEP,
  FONT_WEIGHTS,
  TEXT_ALIGNMENTS,
  LINE_STYLES,
  LINE_THICKNESSES,
  IMAGE_THRESHOLD_DEFAULT,
  IMAGE_THRESHOLD_MIN,
  IMAGE_THRESHOLD_MAX,
  DATE_FORMATS,
  TIME_FORMATS,
  THAI_MONTHS_FULL,
  THAI_MONTHS_SHORT,
  THAI_DAYS_FULL,
  THAI_DAYS_SHORT,
  ENGLISH_MONTHS_FULL,
  ENGLISH_MONTHS_SHORT,
  ENGLISH_DAYS_FULL,
  ENGLISH_DAYS_SHORT,
  COUNTER_DEFAULT_START,
  COUNTER_DEFAULT_PADDING,
  COUNTER_MIN_VALUE,
  COUNTER_MAX_VALUE,
  COUNTER_PADDING_OPTIONS,
  PRINT_DIRECTIONS,
  COLLATION_OPTIONS,
  TEMPLATE_STORAGE_KEY,
  DATA_FILE_CANDIDATES,
  SAMPLE_DATA,
  BARCODE_DEFAULT_WIDTH,
  BARCODE_DEFAULT_HEIGHT,
  BARCODE_DEFAULT_MARGIN,
  QR_DEFAULT_SIZE,
  QR_ERROR_CORRECTION,
  SHAPE_DEFAULTS,
  ELEMENT_DEFAULT_POSITION,
  MAX_HISTORY_SIZE,
  MAX_IMAGE_SIZE,
  ALLOWED_IMAGE_TYPES,
  mmToDots,
  dotsToMm,
  validateImageFile,
  formatNumber,
  clamp
};
