/**
 * Data Management Module
 * Handles data binding, JSON processing, and template management
 */

import { getCanvas } from './canvas-manager.js';
import {
  TEMPLATE_STORAGE_KEY,
  DATA_FILE_CANDIDATES,
  SAMPLE_DATA,
  THAI_MONTHS_FULL,
  THAI_MONTHS_SHORT,
  THAI_DAYS_FULL,
  THAI_DAYS_SHORT,
  ENGLISH_MONTHS_FULL,
  ENGLISH_MONTHS_SHORT,
  ENGLISH_DAYS_FULL,
  ENGLISH_DAYS_SHORT,
  formatNumber
} from './config.js';

// Current data state
let currentData = [];
let currentDataIndex = 0;

/**
 * Initialize data management
 */
export function initializeDataManager() {
  setupDataControls();
  setupTemplateControls();
  loadTemplates();
  tryLoadDefaultData();
}

/**
 * Setup data-related controls
 */
function setupDataControls() {
  const dataJSON = document.getElementById('dataJSON');
  const previewBtn = document.getElementById('previewData');
  const dataIndexInput = document.getElementById('dataIndex');
  
  if (dataJSON) {
    dataJSON.addEventListener('input', renderBindingsList);
  }
  
  if (previewBtn) {
    previewBtn.addEventListener('click', applyDataPreview);
  }
  
  if (dataIndexInput) {
    dataIndexInput.addEventListener('change', applyDataPreview);
  }
}

/**
 * Setup template-related controls
 */
function setupTemplateControls() {
  const saveBtn = document.getElementById('saveTpl');
  const exportBtn = document.getElementById('exportTemplates');
  const importInput = document.getElementById('importTemplates');
  
  if (saveBtn) {
    saveBtn.addEventListener('click', saveTemplate);
  }
  
  if (exportBtn) {
    exportBtn.addEventListener('click', exportTemplates);
  }
  
  if (importInput) {
    importInput.addEventListener('change', importTemplates);
  }
}

/**
 * Parse JSON data from textarea
 */
export function parseDataJSON() {
  const textarea = document.getElementById('dataJSON');
  if (!textarea) return [];
  
  try {
    const data = JSON.parse(textarea.value.trim() || '[]');
    return Array.isArray(data) ? data : [];
  } catch (e) {
    console.error('Invalid JSON data:', e);
    return [];
  }
}

/**
 * Apply data preview to canvas elements
 */
function applyDataPreview() {
  const stage = getCanvas();
  if (!stage) return;
  
  const data = parseDataJSON();
  const indexInput = document.getElementById('dataIndex');
  const statusSpan = document.getElementById('dataStatus');
  
  if (data.length === 0) {
    if (statusSpan) statusSpan.textContent = 'No data available';
    return;
  }
  
  const idx = Math.max(1, Math.min(data.length, parseInt(indexInput?.value || '1', 10)));
  const record = data[idx - 1];
  
  if (!record) {
    if (statusSpan) statusSpan.textContent = 'Invalid data index';
    return;
  }
  
  // Apply data to all objects
  stage.getObjects().forEach(obj => {
    applyDataToObject(obj, record, idx - 1);
  });
  
  stage.requestRenderAll();
  
  if (statusSpan) {
    statusSpan.textContent = `Preview row ${idx}/${data.length}`;
  }
  
  currentData = data;
  currentDataIndex = idx - 1;
}

/**
 * Apply data to a single object
 */
function applyDataToObject(obj, record, index) {
  if (!obj || !record) return;
  
  // Handle different object types
  if (obj._isDateTimeField) {
    updateDateTimeObject(obj);
  } else if (obj._isCounterField) {
    updateCounterObject(obj, index);
  } else if (obj._bindingKey && record[obj._bindingKey] !== undefined) {
    // Regular data binding
    const value = String(record[obj._bindingKey]);
    
    if (obj.type && obj.type.includes('text')) {
      obj.set('text', value);
    } else if (obj._isBarcodeField) {
      updateBarcodeObject(obj, value);
    } else if (obj._isQRField) {
      updateQRObject(obj, value);
    }
  }
}

/**
 * Update date/time object
 */
function updateDateTimeObject(obj) {
  if (!obj._dateTimeConfig) return;
  
  const now = obj._dateTimeConfig.updateMode === 'current' 
    ? new Date(obj._createdAt) 
    : new Date();
    
  const formatted = formatDateTime(
    now,
    obj._dateTimeConfig.dateFormat || 'DD/MM/YYYY',
    obj._dateTimeConfig.yearType || 'BE',
    obj._dateTimeConfig.language || 'th'
  );
  
  obj.set('text', formatted);
}

/**
 * Update counter object
 */
function updateCounterObject(obj, index) {
  if (!obj._isCounterField) return;
  
  const start = obj._counterStart || 1;
  const padding = obj._counterPadding || 3;
  const mode = obj._counterMode || 'data';
  
  let counterValue;
  if (mode === 'data') {
    counterValue = start + index;
  } else {
    // For print mode, this would be calculated during printing
    counterValue = start + index;
  }
  
  const formatted = formatNumber(counterValue, padding);
  obj.set('text', formatted);
}

/**
 * Update barcode object (placeholder)
 */
function updateBarcodeObject(obj, value) {
  // This would regenerate the barcode with new value
  // Implementation would depend on barcode library
  console.log('Update barcode:', value);
}

/**
 * Update QR object (placeholder)
 */
function updateQRObject(obj, value) {
  // This would regenerate the QR code with new value
  // Implementation would depend on QR library
  console.log('Update QR:', value);
}

/**
 * Format date/time string
 */
export function formatDateTime(date, format, yearType = 'BE', language = 'th') {
  const day = date.getDate();
  const month = date.getMonth();
  const year = date.getFullYear() + (yearType === 'BE' ? 543 : 0);
  const dayOfWeek = date.getDay();
  const hours = date.getHours();
  const minutes = date.getMinutes();
  const seconds = date.getSeconds();
  
  const months = language === 'th' ? THAI_MONTHS_FULL : ENGLISH_MONTHS_FULL;
  const monthsShort = language === 'th' ? THAI_MONTHS_SHORT : ENGLISH_MONTHS_SHORT;
  const days = language === 'th' ? THAI_DAYS_FULL : ENGLISH_DAYS_FULL;
  const daysShort = language === 'th' ? THAI_DAYS_SHORT : ENGLISH_DAYS_SHORT;
  
  let result = format;
  
  // Replace date tokens
  result = result.replace(/dddd/g, days[dayOfWeek]);
  result = result.replace(/dd/g, daysShort[dayOfWeek]);
  result = result.replace(/DD/g, String(day).padStart(2, '0'));
  result = result.replace(/mmmm/g, months[month]);
  result = result.replace(/mmm/g, monthsShort[month]);
  result = result.replace(/MM/g, String(month + 1).padStart(2, '0'));
  result = result.replace(/YYYY/g, String(year));
  result = result.replace(/YY/g, String(year).slice(-2));
  
  // Replace time tokens
  result = result.replace(/HH/g, String(hours).padStart(2, '0'));
  result = result.replace(/hh/g, String(hours % 12 || 12).padStart(2, '0'));
  result = result.replace(/mm/g, String(minutes).padStart(2, '0'));
  result = result.replace(/ss/g, String(seconds).padStart(2, '0'));
  result = result.replace(/AM\/PM/g, hours >= 12 ? 'PM' : 'AM');
  
  return result;
}

/**
 * Render bindings list
 */
export function renderBindingsList() {
  const container = document.getElementById('bindingsList');
  const stage = getCanvas();
  
  if (!container || !stage) return;
  
  container.innerHTML = '';
  
  const objects = stage.getObjects();
  if (objects.length === 0) {
    container.innerHTML = '<div class="text-sm text-slate-500">No elements to bind</div>';
    return;
  }
  
  objects.forEach((obj, index) => {
    const bindingItem = createBindingItem(obj, index);
    container.appendChild(bindingItem);
  });
}

/**
 * Create binding item element
 */
function createBindingItem(obj, index) {
  const div = document.createElement('div');
  div.className = 'binding-item';
  
  const label = document.createElement('label');
  label.textContent = getObjectLabel(obj, index);
  
  const input = document.createElement('input');
  input.type = 'text';
  input.value = obj._bindingKey || '';
  input.placeholder = 'data.field_name';
  input.addEventListener('change', (e) => {
    obj._bindingKey = e.target.value;
  });
  
  div.appendChild(label);
  div.appendChild(input);
  
  return div;
}

/**
 * Get object label for binding list
 */
function getObjectLabel(obj, index) {
  if (obj.type && obj.type.includes('text')) {
    const text = obj.text || obj._templateText || '';
    return `Text: ${text.substring(0, 20)}${text.length > 20 ? '...' : ''}`;
  } else if (obj._isBarcodeField) {
    return 'Barcode';
  } else if (obj._isQRField) {
    return 'QR Code';
  } else if (obj._isImageField) {
    return 'Image';
  } else {
    return `Element ${index + 1}`;
  }
}

/**
 * Save current canvas as template
 */
function saveTemplate() {
  const stage = getCanvas();
  if (!stage) return;
  
  const name = prompt('Template name:', `Template ${Date.now()}`);
  if (!name) return;
  
  const template = {
    name,
    data: stage.toJSON(),
    created: new Date().toISOString(),
    paperSize: getCurrentPaperSize()
  };
  
  const templates = getStoredTemplates();
  templates.push(template);
  
  localStorage.setItem(TEMPLATE_STORAGE_KEY, JSON.stringify(templates));
  renderTemplates();
}

/**
 * Load templates from storage
 */
export function loadTemplates() {
  renderTemplates();
}

/**
 * Render templates list
 */
function renderTemplates() {
  const container = document.getElementById('templates');
  if (!container) return;
  
  const templates = getStoredTemplates();
  container.innerHTML = '';
  
  if (templates.length === 0) {
    container.innerHTML = '<div class="text-sm text-slate-500">No saved templates</div>';
    return;
  }
  
  templates.forEach((template, index) => {
    const templateCard = createTemplateCard(template, index);
    container.appendChild(templateCard);
  });
}

/**
 * Create template card element
 */
function createTemplateCard(template, index) {
  const div = document.createElement('div');
  div.className = 'template-card';
  
  const title = document.createElement('div');
  title.className = 'font-medium text-sm mb-1';
  title.textContent = template.name;
  
  const info = document.createElement('div');
  info.className = 'text-xs text-slate-500 mb-2';
  info.textContent = `${template.paperSize || 'Unknown size'} • ${new Date(template.created).toLocaleDateString()}`;
  
  const actions = document.createElement('div');
  actions.className = 'flex gap-1';
  
  const loadBtn = document.createElement('button');
  loadBtn.className = 'text-xs px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600';
  loadBtn.textContent = 'Load';
  loadBtn.addEventListener('click', () => loadTemplate(template));
  
  const deleteBtn = document.createElement('button');
  deleteBtn.className = 'text-xs px-2 py-1 bg-red-500 text-white rounded hover:bg-red-600';
  deleteBtn.textContent = 'Delete';
  deleteBtn.addEventListener('click', () => deleteTemplate(index));
  
  actions.appendChild(loadBtn);
  actions.appendChild(deleteBtn);
  
  div.appendChild(title);
  div.appendChild(info);
  div.appendChild(actions);
  
  return div;
}

/**
 * Load template to canvas
 */
function loadTemplate(template) {
  const stage = getCanvas();
  if (!stage) return;
  
  if (stage.getObjects().length > 0) {
    if (!confirm('This will replace current content. Continue?')) {
      return;
    }
  }
  
  stage.loadFromJSON(template.data, () => {
    stage.requestRenderAll();
    renderBindingsList();
  });
}

/**
 * Delete template
 */
function deleteTemplate(index) {
  if (!confirm('Delete this template?')) return;
  
  const templates = getStoredTemplates();
  templates.splice(index, 1);
  
  localStorage.setItem(TEMPLATE_STORAGE_KEY, JSON.stringify(templates));
  renderTemplates();
}

/**
 * Export templates to JSON file
 */
function exportTemplates() {
  const templates = getStoredTemplates();
  if (templates.length === 0) {
    alert('No templates to export');
    return;
  }
  
  const blob = new Blob([JSON.stringify(templates, null, 2)], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  
  const a = document.createElement('a');
  a.href = url;
  a.download = `label-templates-${new Date().toISOString().split('T')[0]}.json`;
  a.click();
  
  URL.revokeObjectURL(url);
}

/**
 * Import templates from JSON file
 */
function importTemplates(event) {
  const file = event.target.files[0];
  if (!file) return;
  
  const reader = new FileReader();
  reader.onload = (e) => {
    try {
      const importedTemplates = JSON.parse(e.target.result);
      if (!Array.isArray(importedTemplates)) {
        throw new Error('Invalid template file format');
      }
      
      const existingTemplates = getStoredTemplates();
      const mergedTemplates = [...existingTemplates, ...importedTemplates];
      
      localStorage.setItem(TEMPLATE_STORAGE_KEY, JSON.stringify(mergedTemplates));
      renderTemplates();
      
      alert(`Imported ${importedTemplates.length} templates`);
    } catch (error) {
      alert('Failed to import templates: ' + error.message);
    }
  };
  
  reader.readAsText(file);
  event.target.value = ''; // Reset input
}

/**
 * Get stored templates from localStorage
 */
function getStoredTemplates() {
  try {
    const stored = localStorage.getItem(TEMPLATE_STORAGE_KEY);
    return stored ? JSON.parse(stored) : [];
  } catch (e) {
    console.error('Failed to load templates:', e);
    return [];
  }
}

/**
 * Get current paper size info
 */
function getCurrentPaperSize() {
  const select = document.getElementById('paperSize');
  return select ? select.options[select.selectedIndex].text : 'Unknown';
}

/**
 * Try to load default data from files
 */
async function tryLoadDefaultData() {
  const textarea = document.getElementById('dataJSON');
  const statusSpan = document.getElementById('dataStatus');
  
  if (!textarea || textarea.value.trim()) return;
  
  // Only try to fetch if running on HTTP(S)
  if (location.protocol !== 'http:' && location.protocol !== 'https:') {
    // Show sample data for file:// protocol
    textarea.value = JSON.stringify(SAMPLE_DATA, null, 2);
    if (statusSpan) {
      statusSpan.textContent = `Loaded ${SAMPLE_DATA.length} sample records`;
    }
    renderBindingsList();
    return;
  }
  
  // Try to fetch from candidate URLs
  for (const url of DATA_FILE_CANDIDATES) {
    try {
      const response = await fetch(url, { cache: 'no-store' });
      if (!response.ok) continue;
      
      const data = await response.json();
      if (Array.isArray(data)) {
        textarea.value = JSON.stringify(data, null, 2);
        if (statusSpan) {
          statusSpan.textContent = `Loaded ${data.length} records from ${url}`;
        }
        renderBindingsList();
        return;
      }
    } catch (e) {
      // Continue to next candidate
    }
  }
}
