/**
 * Print Management Module
 * Handles printer discovery, print job management, and printing functionality
 */

import { getCanvas, getActualPaperSize, getCurrentOrientation } from './canvas-manager.js';
import { parseDataJSON, formatDateTime } from './data-manager.js';
import { DPI, formatNumber } from './config.js';

// Print state
let availablePrinters = [];
let selectedPrinter = null;

/**
 * Initialize print management
 */
export function initializePrintManager() {
  setupPrintControls();
  refreshPrinters();
}

/**
 * Setup print-related controls
 */
function setupPrintControls() {
  const refreshBtn = document.getElementById('refreshPrinters');
  const printBtn = document.getElementById('print');
  
  if (refreshBtn) {
    refreshBtn.addEventListener('click', refreshPrinters);
  }
  
  if (printBtn) {
    printBtn.addEventListener('click', handlePrint);
  }
}

/**
 * Refresh available printers
 */
export async function refreshPrinters() {
  const printerList = document.getElementById('printerList');
  const statusSpan = document.getElementById('status');
  
  if (!printerList) return;
  
  // Show loading state
  printerList.innerHTML = '<div class="text-sm text-slate-500">Searching for printers...</div>';
  
  try {
    // Try to get printers using Web API (if available)
    if ('getInstalledRelatedApps' in navigator) {
      // This is a placeholder - actual printer discovery would depend on the specific API
      availablePrinters = await discoverPrinters();
    } else {
      // Fallback: show manual printer selection
      availablePrinters = getDefaultPrinters();
    }
    
    renderPrinterList();
    
    if (statusSpan) {
      statusSpan.textContent = `Found ${availablePrinters.length} printer(s)`;
    }
  } catch (error) {
    console.error('Failed to refresh printers:', error);
    printerList.innerHTML = '<div class="text-sm text-red-500">Failed to discover printers</div>';
    
    if (statusSpan) {
      statusSpan.textContent = 'Printer discovery failed';
    }
  }
}

/**
 * Discover printers (placeholder implementation)
 */
async function discoverPrinters() {
  // This would be replaced with actual printer discovery logic
  // For now, return some mock printers
  return getDefaultPrinters();
}

/**
 * Get default/mock printers
 */
function getDefaultPrinters() {
  return [
    { id: 'default', name: 'Default Printer', type: 'system' },
    { id: 'pdf', name: 'Save as PDF', type: 'pdf' },
    { id: 'brother-ql', name: 'Brother QL Series', type: 'label' },
    { id: 'zebra-zp', name: 'Zebra ZP Series', type: 'label' },
    { id: 'dymo-lw', name: 'DYMO LabelWriter', type: 'label' }
  ];
}

/**
 * Render printer list
 */
function renderPrinterList() {
  const printerList = document.getElementById('printerList');
  if (!printerList) return;
  
  printerList.innerHTML = '';
  
  if (availablePrinters.length === 0) {
    printerList.innerHTML = '<div class="text-sm text-slate-500">No printers found</div>';
    return;
  }
  
  availablePrinters.forEach(printer => {
    const printerItem = createPrinterItem(printer);
    printerList.appendChild(printerItem);
  });
}

/**
 * Create printer list item
 */
function createPrinterItem(printer) {
  const div = document.createElement('div');
  div.className = 'printer-item';
  
  const name = document.createElement('div');
  name.className = 'font-medium text-sm';
  name.textContent = printer.name;
  
  const type = document.createElement('div');
  type.className = 'text-xs text-slate-500';
  type.textContent = printer.type.charAt(0).toUpperCase() + printer.type.slice(1) + ' Printer';
  
  div.appendChild(name);
  div.appendChild(type);
  
  div.addEventListener('click', () => selectPrinter(printer));
  
  return div;
}

/**
 * Select a printer
 */
function selectPrinter(printer) {
  selectedPrinter = printer;
  
  // Update UI
  const printerItems = document.querySelectorAll('.printer-item');
  printerItems.forEach(item => item.classList.remove('selected'));
  
  const selectedItem = Array.from(printerItems).find(item => 
    item.querySelector('.font-medium').textContent === printer.name
  );
  
  if (selectedItem) {
    selectedItem.classList.add('selected');
  }
  
  // Update hidden input
  const printerInput = document.getElementById('printer');
  if (printerInput) {
    printerInput.value = printer.id;
  }
  
  const statusSpan = document.getElementById('status');
  if (statusSpan) {
    statusSpan.textContent = `Selected: ${printer.name}`;
  }
}

/**
 * Handle print button click
 */
async function handlePrint() {
  const stage = getCanvas();
  if (!stage) {
    alert('No canvas available');
    return;
  }
  
  if (!selectedPrinter) {
    alert('Please select a printer first');
    return;
  }
  
  const statusSpan = document.getElementById('status');
  const printBtn = document.getElementById('print');
  
  try {
    // Show loading state
    if (printBtn) {
      printBtn.disabled = true;
      printBtn.textContent = 'Printing...';
    }
    
    if (statusSpan) {
      statusSpan.textContent = 'Preparing print job...';
    }
    
    // Get print settings
    const printSettings = getPrintSettings();
    
    // Get data for printing
    const data = parseDataJSON();
    
    if (data.length === 0) {
      // Print single label
      await printSingleLabel(stage, printSettings);
    } else {
      // Print multiple labels with data
      await printMultipleLabels(stage, data, printSettings);
    }
    
    if (statusSpan) {
      statusSpan.textContent = 'Print job completed';
    }
    
  } catch (error) {
    console.error('Print failed:', error);
    alert('Print failed: ' + error.message);
    
    if (statusSpan) {
      statusSpan.textContent = 'Print failed';
    }
  } finally {
    // Restore button state
    if (printBtn) {
      printBtn.disabled = false;
      printBtn.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
          <path d="M6 9V2h12v7H6zm10-2V4H8v3h8zM6 14H4a2 2 0 01-2-2V9a2 2 0 012-2h16a2 2 0 012 2v3a2 2 0 01-2 2h-2v5H6v-5zm2 0v3h8v-3H8z" />
        </svg>
        Print
      `;
    }
  }
}

/**
 * Get current print settings
 */
function getPrintSettings() {
  const copies = parseInt(document.getElementById('copies')?.value || '1', 10);
  const collation = document.getElementById('collation')?.value || 'uncollated';
  const reverseOrder = document.getElementById('reverseOrder')?.checked || false;
  const direction = parseInt(document.getElementById('direction')?.value || '1', 10);
  
  return {
    copies: Math.max(1, copies),
    collation,
    reverseOrder,
    direction,
    printer: selectedPrinter
  };
}

/**
 * Print single label
 */
async function printSingleLabel(stage, settings) {
  const imageData = generatePrintImage(stage);
  
  for (let copy = 0; copy < settings.copies; copy++) {
    await sendToPrinter(imageData, settings);
  }
}

/**
 * Print multiple labels with data
 */
async function printMultipleLabels(stage, data, settings) {
  const statusSpan = document.getElementById('status');
  let printSequence = 0;
  
  // Generate print jobs based on collation
  const printJobs = generatePrintJobs(data, settings);
  
  for (let jobIndex = 0; jobIndex < printJobs.length; jobIndex++) {
    const job = printJobs[jobIndex];
    
    if (statusSpan) {
      statusSpan.textContent = `Printing ${jobIndex + 1}/${printJobs.length}...`;
    }
    
    // Apply data to canvas
    applyDataToCanvas(stage, job.data, job.dataIndex, printSequence);
    
    // Generate image
    const imageData = generatePrintImage(stage);
    
    // Send to printer
    await sendToPrinter(imageData, settings);
    
    printSequence++;
  }
}

/**
 * Generate print jobs based on data and settings
 */
function generatePrintJobs(data, settings) {
  const jobs = [];
  
  if (settings.collation === 'collated') {
    // Collated: print all copies of each record together
    for (let dataIndex = 0; dataIndex < data.length; dataIndex++) {
      for (let copy = 0; copy < settings.copies; copy++) {
        jobs.push({
          data: data[dataIndex],
          dataIndex,
          copy
        });
      }
    }
  } else {
    // Uncollated: print one copy of each record, then repeat
    for (let copy = 0; copy < settings.copies; copy++) {
      for (let dataIndex = 0; dataIndex < data.length; dataIndex++) {
        jobs.push({
          data: data[dataIndex],
          dataIndex,
          copy
        });
      }
    }
  }
  
  // Apply reverse order if requested
  if (settings.reverseOrder) {
    jobs.reverse();
  }
  
  return jobs;
}

/**
 * Apply data to canvas objects
 */
function applyDataToCanvas(stage, record, dataIndex, printSequence) {
  stage.getObjects().forEach(obj => {
    if (obj._isDateTimeField) {
      updateDateTimeObject(obj);
    } else if (obj._isCounterField) {
      updateCounterObject(obj, dataIndex, printSequence);
    } else if (obj._bindingKey && record[obj._bindingKey] !== undefined) {
      const value = String(record[obj._bindingKey]);
      if (obj.type && obj.type.includes('text')) {
        obj.set('text', value);
      }
    }
  });
  
  stage.requestRenderAll();
}

/**
 * Update date/time object for printing
 */
function updateDateTimeObject(obj) {
  if (!obj._dateTimeConfig) return;
  
  const now = obj._dateTimeConfig.updateMode === 'current' 
    ? new Date(obj._createdAt) 
    : new Date();
    
  const formatted = formatDateTime(
    now,
    obj._dateTimeConfig.dateFormat || 'DD/MM/YYYY',
    obj._dateTimeConfig.yearType || 'BE',
    obj._dateTimeConfig.language || 'th'
  );
  
  obj.set('text', formatted);
}

/**
 * Update counter object for printing
 */
function updateCounterObject(obj, dataIndex, printSequence) {
  if (!obj._isCounterField) return;
  
  const start = obj._counterStart || 1;
  const padding = obj._counterPadding || 3;
  const mode = obj._counterMode || 'data';
  
  let counterValue;
  if (mode === 'data') {
    counterValue = start + dataIndex;
  } else {
    counterValue = start + printSequence;
  }
  
  const formatted = formatNumber(counterValue, padding);
  obj.set('text', formatted);
}

/**
 * Generate print image from canvas
 */
function generatePrintImage(stage) {
  const paperSize = getActualPaperSize();
  const orientation = getCurrentOrientation();
  
  // Get canvas as image data
  const dataURL = stage.toDataURL({
    format: 'png',
    quality: 1,
    multiplier: 1 // Use actual canvas resolution
  });
  
  return {
    dataURL,
    width: stage.width,
    height: stage.height,
    paperSize,
    orientation,
    dpi: DPI
  };
}

/**
 * Send image to printer
 */
async function sendToPrinter(imageData, settings) {
  if (!selectedPrinter) {
    throw new Error('No printer selected');
  }
  
  switch (selectedPrinter.type) {
    case 'pdf':
      return await printToPDF(imageData, settings);
    case 'system':
      return await printToSystem(imageData, settings);
    case 'label':
      return await printToLabelPrinter(imageData, settings);
    default:
      throw new Error('Unsupported printer type');
  }
}

/**
 * Print to PDF
 */
async function printToPDF(imageData, settings) {
  // Create a new window with the image for printing
  const printWindow = window.open('', '_blank');
  
  printWindow.document.write(`
    <html>
      <head>
        <title>Label Print</title>
        <style>
          body { margin: 0; padding: 20px; }
          img { max-width: 100%; height: auto; }
          @media print {
            body { margin: 0; padding: 0; }
            img { width: 100%; height: auto; }
          }
        </style>
      </head>
      <body>
        <img src="${imageData.dataURL}" alt="Label" />
      </body>
    </html>
  `);
  
  printWindow.document.close();
  
  // Wait a bit for the image to load, then print
  setTimeout(() => {
    printWindow.print();
  }, 500);
}

/**
 * Print to system printer
 */
async function printToSystem(imageData, settings) {
  // Use browser's print API
  return await printToPDF(imageData, settings);
}

/**
 * Print to label printer (placeholder)
 */
async function printToLabelPrinter(imageData, settings) {
  // This would implement specific label printer protocols
  // For now, fall back to PDF printing
  console.log('Label printer printing not implemented, falling back to PDF');
  return await printToPDF(imageData, settings);
}

/**
 * Get selected printer
 */
export function getSelectedPrinter() {
  return selectedPrinter;
}

/**
 * Set selected printer
 */
export function setSelectedPrinter(printer) {
  selectedPrinter = printer;
}
