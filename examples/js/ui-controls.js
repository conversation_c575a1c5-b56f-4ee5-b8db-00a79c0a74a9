/**
 * UI Controls Module
 * Handles UI event listeners, button controls, and form interactions
 */

import { getCanvas, snapshot, undo, clearCanvas, handlePaperSizeChange, applyCustomSize, setOrientation } from './canvas-manager.js';
import { addText, addBarcode, addQR, addImage, addLine, addRect, addCircle, addTriangle, addDateTime, addCounter } from './element-creator.js';
import { 
  DEFAULT_FONT_FAMILY, 
  DEFAULT_FONT_SIZE, 
  DEFAULT_FONT_WEIGHT,
  FONT_FAMILIES,
  FONT_SIZE_MIN,
  FONT_SIZE_MAX,
  FONT_SIZE_STEP,
  LINE_STYLES,
  LINE_THICKNESSES
} from './config.js';

/**
 * Initialize all UI event listeners
 */
export function initializeUIControls() {
  setupPaperSizeControls();
  setupOrientationControls();
  setupElementCreationControls();
  setupActionControls();
  setupTextControls();
  setupShapeControls();
  setupImageControls();
  setupDateTimeControls();
  setupCounterControls();
  setupKeyboardShortcuts();
  
  // Make updateActionButtons globally available
  window.updateActionButtons = updateActionButtons;
}

/**
 * Setup paper size controls
 */
function setupPaperSizeControls() {
  const paperSizeSelect = document.getElementById('paperSize');
  const applyCustomBtn = document.getElementById('applyCustomSize');
  
  if (paperSizeSelect) {
    paperSizeSelect.addEventListener('change', handlePaperSizeChange);
  }
  
  if (applyCustomBtn) {
    applyCustomBtn.addEventListener('click', applyCustomSize);
  }
}

/**
 * Setup orientation controls
 */
function setupOrientationControls() {
  const portraitBtn = document.getElementById('orientationPortrait');
  const landscapeBtn = document.getElementById('orientationLandscape');
  
  if (portraitBtn) {
    portraitBtn.addEventListener('click', () => setOrientation('portrait'));
  }
  
  if (landscapeBtn) {
    landscapeBtn.addEventListener('click', () => setOrientation('landscape'));
  }
}

/**
 * Setup element creation controls
 */
function setupElementCreationControls() {
  // Text
  const addTextBtn = document.getElementById('addText');
  if (addTextBtn) {
    addTextBtn.addEventListener('click', () => {
      const text = prompt('Text:', 'ทดสอบ / HELLO') || '';
      if (text) addText(text);
    });
  }
  
  // Barcode
  const addBarcodeBtn = document.getElementById('addBarcode');
  if (addBarcodeBtn) {
    addBarcodeBtn.addEventListener('click', async () => {
      const value = prompt('Code128 value:', '1234567890') || '';
      if (value) await addBarcode(value);
    });
  }
  
  // QR Code
  const addQRBtn = document.getElementById('addQR');
  if (addQRBtn) {
    addQRBtn.addEventListener('click', async () => {
      const value = prompt('QR Code value:', 'https://example.com') || '';
      if (value) await addQR(value);
    });
  }
  
  // Image
  const imgFileInput = document.getElementById('imgFile');
  if (imgFileInput) {
    imgFileInput.addEventListener('change', (e) => {
      const file = e.target.files[0];
      if (file) {
        addImage(file);
        e.target.value = ''; // Reset input
      }
    });
  }
  
  // Shapes
  const addLineBtn = document.getElementById('addLine');
  if (addLineBtn) addLineBtn.addEventListener('click', addLine);
  
  const addRectBtn = document.getElementById('addRect');
  if (addRectBtn) addRectBtn.addEventListener('click', addRect);
  
  const addCircleBtn = document.getElementById('addCircle');
  if (addCircleBtn) addCircleBtn.addEventListener('click', addCircle);
  
  const addTriangleBtn = document.getElementById('addTriangle');
  if (addTriangleBtn) addTriangleBtn.addEventListener('click', addTriangle);
  
  // Date/Time
  const addDateTimeBtn = document.getElementById('addDateTime');
  if (addDateTimeBtn) addDateTimeBtn.addEventListener('click', addDateTime);
  
  // Counter
  const addCounterBtn = document.getElementById('addCounter');
  if (addCounterBtn) addCounterBtn.addEventListener('click', addCounter);
}

/**
 * Setup action controls (undo, copy, delete, etc.)
 */
function setupActionControls() {
  // Undo
  const undoBtn = document.getElementById('undoBtn');
  if (undoBtn) {
    undoBtn.addEventListener('click', undo);
  }
  
  // Copy
  const copyBtn = document.getElementById('copy');
  if (copyBtn) {
    copyBtn.addEventListener('click', copySelectedObject);
  }
  
  // Delete
  const deleteBtn = document.getElementById('del');
  if (deleteBtn) {
    deleteBtn.addEventListener('click', deleteSelectedObject);
  }
  
  // Clear Canvas
  const clearBtn = document.getElementById('clearCanvas');
  if (clearBtn) {
    clearBtn.addEventListener('click', () => {
      if (confirm('Are you sure you want to clear all elements?')) {
        clearCanvas();
      }
    });
  }
  
  // Bring to front
  const bringFrontBtn = document.getElementById('bringFront');
  if (bringFrontBtn) {
    bringFrontBtn.addEventListener('click', bringToFront);
  }
  
  // Send to back
  const sendBackBtn = document.getElementById('sendBack');
  if (sendBackBtn) {
    sendBackBtn.addEventListener('click', sendToBack);
  }
}

/**
 * Setup text controls
 */
function setupTextControls() {
  // Font family buttons
  const fontButtons = document.querySelectorAll('.tt-font');
  fontButtons.forEach(btn => {
    btn.addEventListener('click', () => {
      const font = btn.dataset.font;
      setFontFamily(font);
      updateFontFamilyButtons(font);
    });
  });
  
  // Font size controls
  const fsDecBtn = document.getElementById('fsDec');
  const fsIncBtn = document.getElementById('fsInc');
  
  if (fsDecBtn) {
    fsDecBtn.addEventListener('click', () => adjustFontSize(-FONT_SIZE_STEP));
  }
  
  if (fsIncBtn) {
    fsIncBtn.addEventListener('click', () => adjustFontSize(FONT_SIZE_STEP));
  }
  
  // Style buttons
  const boldBtn = document.getElementById('stBold');
  const italicBtn = document.getElementById('stItalic');
  const underlineBtn = document.getElementById('stUnderline');
  const strikeBtn = document.getElementById('stStrike');
  
  if (boldBtn) boldBtn.addEventListener('click', () => toggleTextStyle('bold'));
  if (italicBtn) italicBtn.addEventListener('click', () => toggleTextStyle('italic'));
  if (underlineBtn) underlineBtn.addEventListener('click', () => toggleTextStyle('underline'));
  if (strikeBtn) strikeBtn.addEventListener('click', () => toggleTextStyle('linethrough'));
  
  // Alignment buttons
  const leftBtn = document.getElementById('alLeft');
  const centerBtn = document.getElementById('alCenter');
  const rightBtn = document.getElementById('alRight');
  
  if (leftBtn) leftBtn.addEventListener('click', () => setTextAlign('left'));
  if (centerBtn) centerBtn.addEventListener('click', () => setTextAlign('center'));
  if (rightBtn) rightBtn.addEventListener('click', () => setTextAlign('right'));
}

/**
 * Setup shape controls
 */
function setupShapeControls() {
  // Line style buttons
  const solidBtn = document.getElementById('lineStyleSolid');
  const dashedBtn = document.getElementById('lineStyleDashed');
  const dottedBtn = document.getElementById('lineStyleDotted');
  const dashDotBtn = document.getElementById('lineStyleDashDot');
  
  if (solidBtn) solidBtn.addEventListener('click', () => setLineStyle('solid'));
  if (dashedBtn) dashedBtn.addEventListener('click', () => setLineStyle('dashed'));
  if (dottedBtn) dottedBtn.addEventListener('click', () => setLineStyle('dotted'));
  if (dashDotBtn) dashDotBtn.addEventListener('click', () => setLineStyle('dashDot'));
  
  // Thickness buttons
  LINE_THICKNESSES.forEach(thickness => {
    const btn = document.getElementById(`thickness${thickness}`);
    if (btn) {
      btn.addEventListener('click', () => setLineThickness(thickness));
    }
  });
  
  // Fill buttons
  const fillNoneBtn = document.getElementById('fillNone');
  const fillSolidBtn = document.getElementById('fillSolid');
  
  if (fillNoneBtn) fillNoneBtn.addEventListener('click', () => setFillStyle('none'));
  if (fillSolidBtn) fillSolidBtn.addEventListener('click', () => setFillStyle('solid'));
}

/**
 * Setup image controls
 */
function setupImageControls() {
  const thresholdSlider = document.getElementById('imgThreshold');
  const ditherCheckbox = document.getElementById('imgDither');
  
  if (thresholdSlider) {
    thresholdSlider.addEventListener('input', updateImageSettings);
  }
  
  if (ditherCheckbox) {
    ditherCheckbox.addEventListener('change', updateImageSettings);
  }
}

/**
 * Setup date/time controls
 */
function setupDateTimeControls() {
  // Type buttons
  const dateBtn = document.getElementById('dtTypeDate');
  const timeBtn = document.getElementById('dtTypeTime');
  const dateTimeBtn = document.getElementById('dtTypeDateTime');
  
  if (dateBtn) dateBtn.addEventListener('click', () => setDateTimeType('date'));
  if (timeBtn) timeBtn.addEventListener('click', () => setDateTimeType('time'));
  if (dateTimeBtn) dateTimeBtn.addEventListener('click', () => setDateTimeType('datetime'));
  
  // Format selects
  const dateFormatSelect = document.getElementById('dateFormat');
  const timeFormatSelect = document.getElementById('timeFormat');
  
  if (dateFormatSelect) {
    dateFormatSelect.addEventListener('change', updateDateTimeSettings);
  }
  
  if (timeFormatSelect) {
    timeFormatSelect.addEventListener('change', updateDateTimeSettings);
  }
  
  // Year type and language buttons
  const ceBtn = document.getElementById('yearTypeCE');
  const beBtn = document.getElementById('yearTypeBE');
  const enBtn = document.getElementById('langEN');
  const thBtn = document.getElementById('langTH');
  
  if (ceBtn) ceBtn.addEventListener('click', () => setYearType('CE'));
  if (beBtn) beBtn.addEventListener('click', () => setYearType('BE'));
  if (enBtn) enBtn.addEventListener('click', () => setLanguage('en'));
  if (thBtn) thBtn.addEventListener('click', () => setLanguage('th'));
}

/**
 * Setup counter controls
 */
function setupCounterControls() {
  const startInput = document.getElementById('counterStart');
  const paddingSelect = document.getElementById('counterPadding');
  const modeRadios = document.querySelectorAll('input[name="counterMode"]');
  
  if (startInput) {
    startInput.addEventListener('change', updateCounterSettings);
  }
  
  if (paddingSelect) {
    paddingSelect.addEventListener('change', updateCounterSettings);
  }
  
  modeRadios.forEach(radio => {
    radio.addEventListener('change', updateCounterSettings);
  });
}

/**
 * Setup keyboard shortcuts
 */
function setupKeyboardShortcuts() {
  window.addEventListener('keydown', (ev) => {
    // ESC key: deselect all objects
    if (ev.key === 'Escape') {
      const stage = getCanvas();
      if (stage) {
        stage.discardActiveObject();
        stage.requestRenderAll();
        updateActionButtons();
      }
      ev.preventDefault();
      return;
    }
    
    // Ctrl/Cmd+Z: Undo
    if ((ev.ctrlKey || ev.metaKey) && (ev.key === 'z' || ev.key === 'Z')) {
      ev.preventDefault();
      undo();
    }
  });
}

/**
 * Update action button states based on selection
 */
export function updateActionButtons() {
  const stage = getCanvas();
  if (!stage) return;
  
  const activeObject = stage.getActiveObject();
  const hasSelection = !!activeObject;
  
  // Update button states
  updateButtonState('copy', hasSelection);
  updateButtonState('del', hasSelection);
  updateButtonState('bringFront', hasSelection);
  updateButtonState('sendBack', hasSelection);
  
  // Show/hide control panels based on selection type
  updateControlPanels(activeObject);
}

/**
 * Update button enabled/disabled state
 */
function updateButtonState(buttonId, enabled) {
  const button = document.getElementById(buttonId);
  if (!button) return;
  
  if (enabled) {
    button.disabled = false;
    button.classList.remove('bg-slate-200', 'text-slate-400');
    button.classList.add('bg-slate-200', 'hover:bg-slate-300', 'text-slate-700');
  } else {
    button.disabled = true;
    button.classList.remove('hover:bg-slate-300', 'text-slate-700');
    button.classList.add('bg-slate-200', 'text-slate-400');
  }
}

/**
 * Update control panels visibility based on selected object
 */
function updateControlPanels(activeObject) {
  // Hide all control panels first
  const panels = ['textControls', 'shapeControls', 'imgControls', 'dateTimeControls', 'counterControls'];
  panels.forEach(panelId => {
    const panel = document.getElementById(panelId);
    if (panel) panel.style.display = 'none';
  });
  
  if (!activeObject) return;
  
  // Show appropriate panel based on object type
  if (activeObject.type && activeObject.type.includes('text')) {
    const panel = document.getElementById('textControls');
    if (panel) panel.style.display = 'block';
    updateTextControlsState(activeObject);
  } else if (activeObject._isImageField) {
    const panel = document.getElementById('imgControls');
    if (panel) panel.style.display = 'block';
  } else if (activeObject._isDateTimeField) {
    const panel = document.getElementById('dateTimeControls');
    if (panel) panel.style.display = 'block';
  } else if (activeObject._isCounterField) {
    const panel = document.getElementById('counterControls');
    if (panel) panel.style.display = 'block';
  } else if (['line', 'rect', 'circle', 'triangle'].some(t => activeObject.type?.includes(t))) {
    const panel = document.getElementById('shapeControls');
    if (panel) panel.style.display = 'block';
  }
}

/**
 * Update text controls state based on selected text object
 */
function updateTextControlsState(textObject) {
  // Update font family buttons
  const fontFamily = textObject.fontFamily || DEFAULT_FONT_FAMILY;
  let selectedFont = DEFAULT_FONT_FAMILY;
  
  for (const [key, value] of Object.entries(FONT_FAMILIES)) {
    if (fontFamily.includes(key)) {
      selectedFont = key;
      break;
    }
  }
  
  updateFontFamilyButtons(selectedFont);
  
  // Update font size display
  const fontSize = textObject.fontSize || DEFAULT_FONT_SIZE;
  const fsDisp = document.getElementById('fsDisp');
  if (fsDisp) fsDisp.textContent = fontSize;
  
  // Update hidden inputs
  const fontFamilyInput = document.getElementById('fontFamily');
  const fontSizeInput = document.getElementById('fontSize');
  const fontWeightInput = document.getElementById('fontWeight');
  const textAlignInput = document.getElementById('textAlign');
  
  if (fontFamilyInput) fontFamilyInput.value = selectedFont;
  if (fontSizeInput) fontSizeInput.value = fontSize;
  if (fontWeightInput) fontWeightInput.value = textObject.fontWeight || DEFAULT_FONT_WEIGHT;
  if (textAlignInput) textAlignInput.value = textObject.textAlign || 'left';
}

/**
 * Update font family button states
 */
function updateFontFamilyButtons(selectedFont) {
  const fontButtons = document.querySelectorAll('.tt-font');
  fontButtons.forEach(btn => {
    btn.classList.remove('active', 'bg-blue-500', 'text-white');
    if (btn.dataset.font === selectedFont) {
      btn.classList.add('active', 'bg-blue-500', 'text-white');
    }
  });
}

// Placeholder functions - these would be implemented with full functionality
function copySelectedObject() { /* Implementation */ }
function deleteSelectedObject() { /* Implementation */ }
function bringToFront() { /* Implementation */ }
function sendToBack() { /* Implementation */ }
function setFontFamily(font) { /* Implementation */ }
function adjustFontSize(delta) { /* Implementation */ }
function toggleTextStyle(style) { /* Implementation */ }
function setTextAlign(align) { /* Implementation */ }
function setLineStyle(style) { /* Implementation */ }
function setLineThickness(thickness) { /* Implementation */ }
function setFillStyle(style) { /* Implementation */ }
function updateImageSettings() { /* Implementation */ }
function setDateTimeType(type) { /* Implementation */ }
function updateDateTimeSettings() { /* Implementation */ }
function setYearType(type) { /* Implementation */ }
function setLanguage(lang) { /* Implementation */ }
function updateCounterSettings() { /* Implementation */ }
