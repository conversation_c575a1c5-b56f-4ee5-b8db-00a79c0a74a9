/**
 * Element Creator Module
 * Functions for creating and adding different types of elements to the canvas
 */

import { getCanvas, snapshot } from './canvas-manager.js';
import {
  DEFAULT_FONT_FAMILY,
  DEFAULT_FONT_SIZE,
  DEFAULT_FONT_WEIGHT,
  DEFAULT_TEXT_ALIGN,
  FONT_FAMILIES,
  ELEMENT_DEFAULT_POSITION,
  BARCODE_DEFAULT_WIDTH,
  BARCODE_DEFAULT_HEIGHT,
  BARCODE_DEFAULT_MARGIN,
  QR_DEFAULT_SIZE,
  SHAPE_DEFAULTS,
  COUNTER_DEFAULT_START,
  COUNTER_DEFAULT_PADDING,
  validateImageFile
} from './config.js';

/**
 * Add text element to canvas
 */
export function addText(text = 'ทดสอบ / HELLO') {
  const stage = getCanvas();
  if (!stage) return;

  const familySel = document.getElementById('fontFamily')?.value || DEFAULT_FONT_FAMILY;
  const family = FONT_FAMILIES[familySel] || FONT_FAMILIES[DEFAULT_FONT_FAMILY];
  
  const size = Math.max(8, parseInt(document.getElementById('fontSize')?.value, 10) || DEFAULT_FONT_SIZE);
  const weight = Math.max(100, Math.min(900, parseInt(document.getElementById('fontWeight')?.value, 10) || DEFAULT_FONT_WEIGHT));
  const align = (document.getElementById('textAlign')?.value || DEFAULT_TEXT_ALIGN).toLowerCase();

  const obj = new fabric.Textbox(text, {
    left: ELEMENT_DEFAULT_POSITION.left,
    top: ELEMENT_DEFAULT_POSITION.top,
    fontFamily: family,
    fontSize: size,
    fontWeight: String(weight),
    textAlign: ['left', 'center', 'right'].includes(align) ? align : 'left',
    fill: '#000',
    editable: true,
  });

  obj._templateText = text;
  obj._bindingKey = '';
  
  stage.add(obj).setActiveObject(obj);
  updateActionButtons();
  snapshot();
}

/**
 * Add barcode element to canvas
 */
export async function addBarcode(value = '1234567890') {
  const stage = getCanvas();
  if (!stage) return;

  try {
    // Try to load JsBarcode library
    await tryLoadScripts([
      'https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js',
      './lib/JsBarcode.all.min.js'
    ]);

    if (typeof JsBarcode === 'undefined') {
      throw new Error('JsBarcode library not available');
    }

    // Create canvas for barcode
    const canvas = document.createElement('canvas');
    JsBarcode(canvas, value, {
      format: 'CODE128',
      width: BARCODE_DEFAULT_WIDTH,
      height: BARCODE_DEFAULT_HEIGHT,
      margin: BARCODE_DEFAULT_MARGIN,
      displayValue: false
    });

    // Convert to fabric image
    const img = new fabric.Image(canvas, {
      left: ELEMENT_DEFAULT_POSITION.left,
      top: ELEMENT_DEFAULT_POSITION.top,
      selectable: true,
    });

    img._templateText = value;
    img._bindingKey = '';
    img._isBarcodeField = true;

    stage.add(img).setActiveObject(img);
    updateActionButtons();
    snapshot();
  } catch (error) {
    console.error('Failed to create barcode:', error);
    alert('Failed to create barcode. Please check if the barcode library is available.');
  }
}

/**
 * Add QR code element to canvas
 */
export async function addQR(value = 'https://example.com') {
  const stage = getCanvas();
  if (!stage) return;

  try {
    // Check for qrcode library (qrcode-generator)
    if (typeof qrcode === 'undefined') {
      console.warn('QRCode library not available, creating placeholder');
      // Create a placeholder text instead
      addText(`QR: ${value}`);
      return;
    }

    // Create QR code using qrcode-generator
    const qr = qrcode(0, 'M'); // Type 0 (auto), Error correction level M
    qr.addData(value);
    qr.make();

    // Create canvas and draw QR code
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const moduleCount = qr.getModuleCount();
    const cellSize = Math.floor(QR_DEFAULT_SIZE / moduleCount);
    const canvasSize = cellSize * moduleCount;

    canvas.width = canvasSize;
    canvas.height = canvasSize;

    // Draw QR code
    ctx.fillStyle = '#FFFFFF';
    ctx.fillRect(0, 0, canvasSize, canvasSize);
    ctx.fillStyle = '#000000';

    for (let row = 0; row < moduleCount; row++) {
      for (let col = 0; col < moduleCount; col++) {
        if (qr.isDark(row, col)) {
          ctx.fillRect(col * cellSize, row * cellSize, cellSize, cellSize);
        }
      }
    }

    // Convert to fabric image
    const img = new fabric.Image(canvas, {
      left: ELEMENT_DEFAULT_POSITION.left,
      top: ELEMENT_DEFAULT_POSITION.top,
      selectable: true,
    });

    img._templateText = value;
    img._bindingKey = '';
    img._isQRField = true;

    stage.add(img).setActiveObject(img);
    updateActionButtons();
    snapshot();

  } catch (error) {
    console.error('Failed to create QR code:', error);
    // Fallback to text
    addText(`QR: ${value}`);
  }
}

/**
 * Add image element to canvas
 */
export function addImage(file) {
  const stage = getCanvas();
  if (!stage) return;

  const validation = validateImageFile(file);
  if (!validation.valid) {
    alert(validation.error);
    return;
  }

  const reader = new FileReader();
  reader.onload = function(e) {
    fabric.Image.fromURL(e.target.result, function(img) {
      // Scale image to fit within reasonable bounds
      const maxWidth = 200;
      const maxHeight = 200;
      
      if (img.width > maxWidth || img.height > maxHeight) {
        const scale = Math.min(maxWidth / img.width, maxHeight / img.height);
        img.scale(scale);
      }

      img.set({
        left: ELEMENT_DEFAULT_POSITION.left,
        top: ELEMENT_DEFAULT_POSITION.top,
        selectable: true,
      });

      img._templateText = file.name;
      img._bindingKey = '';
      img._isImageField = true;

      stage.add(img).setActiveObject(img);
      updateActionButtons();
      snapshot();
    });
  };
  reader.readAsDataURL(file);
}

/**
 * Add line element to canvas
 */
export function addLine() {
  const stage = getCanvas();
  if (!stage) return;

  const line = new fabric.Line([10, 10, 100, 10], {
    ...SHAPE_DEFAULTS.line,
    left: ELEMENT_DEFAULT_POSITION.left,
    top: ELEMENT_DEFAULT_POSITION.top,
    selectable: true,
  });

  line._templateText = 'Line';
  line._bindingKey = '';

  stage.add(line).setActiveObject(line);
  updateActionButtons();
  snapshot();
}

/**
 * Add rectangle element to canvas
 */
export function addRect() {
  const stage = getCanvas();
  if (!stage) return;

  const rect = new fabric.Rect({
    ...SHAPE_DEFAULTS.rect,
    left: ELEMENT_DEFAULT_POSITION.left,
    top: ELEMENT_DEFAULT_POSITION.top,
    width: 80,
    height: 50,
    selectable: true,
  });

  rect._templateText = 'Rectangle';
  rect._bindingKey = '';

  stage.add(rect).setActiveObject(rect);
  updateActionButtons();
  snapshot();
}

/**
 * Add circle element to canvas
 */
export function addCircle() {
  const stage = getCanvas();
  if (!stage) return;

  const circle = new fabric.Circle({
    ...SHAPE_DEFAULTS.circle,
    left: ELEMENT_DEFAULT_POSITION.left,
    top: ELEMENT_DEFAULT_POSITION.top,
    radius: 30,
    selectable: true,
  });

  circle._templateText = 'Circle';
  circle._bindingKey = '';

  stage.add(circle).setActiveObject(circle);
  updateActionButtons();
  snapshot();
}

/**
 * Add triangle element to canvas
 */
export function addTriangle() {
  const stage = getCanvas();
  if (!stage) return;

  const triangle = new fabric.Triangle({
    ...SHAPE_DEFAULTS.triangle,
    left: ELEMENT_DEFAULT_POSITION.left,
    top: ELEMENT_DEFAULT_POSITION.top,
    width: 60,
    height: 60,
    selectable: true,
  });

  triangle._templateText = 'Triangle';
  triangle._bindingKey = '';

  stage.add(triangle).setActiveObject(triangle);
  updateActionButtons();
  snapshot();
}

/**
 * Add date/time element to canvas
 */
export function addDateTime() {
  const stage = getCanvas();
  if (!stage) return;

  const now = new Date();
  const defaultFormat = 'DD/MM/YYYY';
  const dateTimeStr = formatDateTime(now, defaultFormat, 'BE', 'th');

  const obj = new fabric.Textbox(dateTimeStr, {
    left: ELEMENT_DEFAULT_POSITION.left,
    top: ELEMENT_DEFAULT_POSITION.top,
    fontFamily: 'Sarabun, Noto Sans Thai, sans-serif',
    fontSize: 20,
    fontWeight: '600',
    fill: '#000',
    editable: false,
  });

  obj._templateText = dateTimeStr;
  obj._bindingKey = '';
  obj._isDateTimeField = true;
  obj._createdAt = now.getTime();
  obj._dateTimeConfig = {
    type: 'date',
    dateFormat: defaultFormat,
    timeFormat: 'HH:mm:ss',
    yearType: 'BE',
    language: 'th',
    updateMode: 'current'
  };

  stage.add(obj).setActiveObject(obj);
  updateActionButtons();
  snapshot();
}

/**
 * Add counter element to canvas
 */
export function addCounter() {
  const stage = getCanvas();
  if (!stage) return;

  const obj = new fabric.Textbox('001', {
    left: ELEMENT_DEFAULT_POSITION.left,
    top: ELEMENT_DEFAULT_POSITION.top,
    fontFamily: 'Sarabun, Noto Sans Thai, sans-serif',
    fontSize: 24,
    fontWeight: '700',
    fill: '#000',
    editable: true,
  });

  obj._templateText = '001';
  obj._bindingKey = '';
  obj._isCounterField = true;
  obj._counterStart = COUNTER_DEFAULT_START;
  obj._counterPadding = COUNTER_DEFAULT_PADDING;
  obj._counterMode = 'data';

  stage.add(obj).setActiveObject(obj);
  updateActionButtons();
  snapshot();
}

/**
 * Load script dynamically
 */
function loadScript(src) {
  return new Promise((resolve, reject) => {
    const s = document.createElement('script');
    s.src = src;
    s.async = true;
    s.onload = () => resolve(src);
    s.onerror = (e) => reject(e);
    document.head.appendChild(s);
  });
}

/**
 * Try loading scripts from multiple sources
 */
async function tryLoadScripts(sources) {
  let lastErr;
  for (const src of sources) {
    try {
      await loadScript(src);
      return src;
    } catch (e) {
      lastErr = e;
    }
  }
  throw lastErr || new Error('All sources failed');
}

/**
 * Format date/time (simplified version - full implementation would be in data-manager.js)
 */
function formatDateTime(date, format, yearType, language) {
  // This is a simplified implementation
  // Full implementation would be in data-manager.js
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  let year = date.getFullYear();
  
  if (yearType === 'BE') {
    year += 543;
  }
  
  return `${day}/${month}/${year}`;
}

/**
 * Update action buttons (placeholder - implemented in ui-controls.js)
 */
function updateActionButtons() {
  if (window.updateActionButtons) {
    window.updateActionButtons();
  }
}
