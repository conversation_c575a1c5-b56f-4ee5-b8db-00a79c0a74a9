/**
 * Main Application Module
 * Initializes and coordinates all modules for the Label Editor
 */

import { initializeCanvas } from './canvas-manager.js';
import { initializeUIControls } from './ui-controls.js';
import { initializeDataManager } from './data-manager.js';
import { initializePrintManager } from './print-manager.js';

/**
 * Application state
 */
const app = {
  initialized: false,
  canvas: null,
  version: '1.0.0'
};

/**
 * Initialize the entire application
 */
async function initializeApp() {
  try {
    console.log('🚀 Initializing Label Editor...');
    
    // Check if required libraries are loaded
    if (!checkRequiredLibraries()) {
      throw new Error('Required libraries not loaded');
    }
    
    // Initialize canvas first
    console.log('📐 Initializing canvas...');
    app.canvas = initializeCanvas();
    
    if (!app.canvas) {
      throw new Error('Failed to initialize canvas');
    }
    
    // Initialize UI controls
    console.log('🎛️ Initializing UI controls...');
    initializeUIControls();
    
    // Initialize data management
    console.log('📊 Initializing data manager...');
    initializeDataManager();
    
    // Initialize print management
    console.log('🖨️ Initializing print manager...');
    initializePrintManager();
    
    // Set up global error handling
    setupErrorHandling();
    
    // Set up performance monitoring
    setupPerformanceMonitoring();
    
    // Mark as initialized
    app.initialized = true;
    
    console.log('✅ Label Editor initialized successfully');
    
    // Show welcome message (optional)
    showWelcomeMessage();
    
  } catch (error) {
    console.error('❌ Failed to initialize Label Editor:', error);
    showErrorMessage('Failed to initialize application: ' + error.message);
  }
}

/**
 * Check if required libraries are loaded
 */
function checkRequiredLibraries() {
  const required = [
    { name: 'Fabric.js', check: () => typeof fabric !== 'undefined' },
    { name: 'QRCode', check: () => typeof QRCode !== 'undefined' }
  ];
  
  const missing = required.filter(lib => !lib.check());
  
  if (missing.length > 0) {
    console.error('Missing required libraries:', missing.map(lib => lib.name));
    return false;
  }
  
  return true;
}

/**
 * Set up global error handling
 */
function setupErrorHandling() {
  // Handle uncaught errors
  window.addEventListener('error', (event) => {
    console.error('Uncaught error:', event.error);
    showErrorMessage('An unexpected error occurred. Please refresh the page.');
  });
  
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    showErrorMessage('An unexpected error occurred. Please refresh the page.');
  });
}

/**
 * Set up performance monitoring
 */
function setupPerformanceMonitoring() {
  // Monitor canvas performance
  if (app.canvas) {
    let renderCount = 0;
    const originalRender = app.canvas.requestRenderAll;
    
    app.canvas.requestRenderAll = function() {
      renderCount++;
      
      // Log performance warning if too many renders
      if (renderCount > 100) {
        console.warn('High render count detected:', renderCount);
        renderCount = 0; // Reset counter
      }
      
      return originalRender.call(this);
    };
  }
  
  // Monitor memory usage (if available)
  if ('memory' in performance) {
    setInterval(() => {
      const memory = performance.memory;
      if (memory.usedJSHeapSize > 50 * 1024 * 1024) { // 50MB
        console.warn('High memory usage detected:', {
          used: Math.round(memory.usedJSHeapSize / 1024 / 1024) + 'MB',
          total: Math.round(memory.totalJSHeapSize / 1024 / 1024) + 'MB'
        });
      }
    }, 30000); // Check every 30 seconds
  }
}

/**
 * Show welcome message
 */
function showWelcomeMessage() {
  const statusSpan = document.getElementById('status');
  if (statusSpan) {
    statusSpan.textContent = 'Ready to create labels';
    statusSpan.style.color = '#059669'; // Green color
    
    // Clear message after 3 seconds
    setTimeout(() => {
      statusSpan.textContent = '';
      statusSpan.style.color = '';
    }, 3000);
  }
}

/**
 * Show error message to user
 */
function showErrorMessage(message) {
  const statusSpan = document.getElementById('status');
  if (statusSpan) {
    statusSpan.textContent = message;
    statusSpan.style.color = '#dc2626'; // Red color
  }
  
  // Also show as alert for critical errors
  if (message.includes('Failed to initialize')) {
    alert(message);
  }
}

/**
 * Get application instance
 */
export function getApp() {
  return app;
}

/**
 * Check if application is initialized
 */
export function isInitialized() {
  return app.initialized;
}

/**
 * Get application version
 */
export function getVersion() {
  return app.version;
}

/**
 * Restart application
 */
export async function restartApp() {
  console.log('🔄 Restarting application...');
  
  // Clear canvas
  if (app.canvas) {
    app.canvas.clear();
  }
  
  // Reset state
  app.initialized = false;
  app.canvas = null;
  
  // Reinitialize
  await initializeApp();
}

/**
 * Clean up application resources
 */
export function cleanup() {
  console.log('🧹 Cleaning up application...');
  
  if (app.canvas) {
    app.canvas.dispose();
    app.canvas = null;
  }
  
  app.initialized = false;
}

/**
 * Export debug information
 */
export function getDebugInfo() {
  return {
    version: app.version,
    initialized: app.initialized,
    canvasObjects: app.canvas ? app.canvas.getObjects().length : 0,
    userAgent: navigator.userAgent,
    timestamp: new Date().toISOString(),
    performance: {
      memory: 'memory' in performance ? {
        used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + 'MB',
        total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + 'MB'
      } : 'Not available'
    }
  };
}

/**
 * Handle page visibility changes
 */
document.addEventListener('visibilitychange', () => {
  if (document.hidden) {
    console.log('📱 Page hidden - pausing non-essential operations');
  } else {
    console.log('📱 Page visible - resuming operations');
    
    // Refresh canvas if needed
    if (app.canvas) {
      app.canvas.requestRenderAll();
    }
  }
});

/**
 * Handle page unload
 */
window.addEventListener('beforeunload', (event) => {
  // Check if there are unsaved changes
  if (app.canvas && app.canvas.getObjects().length > 0) {
    const message = 'You have unsaved changes. Are you sure you want to leave?';
    event.returnValue = message;
    return message;
  }
});

/**
 * Initialize when DOM is ready
 */
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeApp);
} else {
  // DOM is already ready
  initializeApp();
}

// Make app available globally for debugging
window.LabelEditor = {
  getApp,
  isInitialized,
  getVersion,
  restartApp,
  cleanup,
  getDebugInfo
};

console.log('📦 Label Editor modules loaded');
