<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Label Editor (60×20 mm @203dpi)</title>
  
  <!-- External Libraries -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@400;600;700&family=Noto+Sans+Thai:wght@400;600&display=swap" rel="stylesheet" />
  <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
  
  <!-- Application Styles -->
  <link rel="stylesheet" href="./css/styles.css" />

  <!-- Local Libraries - Load synchronously before modules -->
  <script src="./lib/fabric.min.js"></script>
  <script src="./lib/qrcode.min.js"></script>
</head>
<body>
  <div class="max-w-7xl mx-auto p-4">
    <h1 class="text-xl font-semibold mb-4">
      Label Editor (60×20 mm @203dpi)
    </h1>
    
    <div class="flex flex-col lg:flex-row gap-4">
      <!-- Left Panel: Controls -->
      <div class="flex-1 flex flex-col gap-2">
        
        <!-- Label Size Section -->
        <div class="bg-white border rounded p-3">
          <h3 class="font-medium mb-2">Label Size</h3>
          <div class="flex flex-col gap-2">
            <label class="text-sm text-slate-600">Paper Size</label>
            <select id="paperSize" class="border rounded px-2 py-1">
              <option value="60x20" selected>60×20 mm</option>
              <option value="50x20">50×20 mm</option>
              <option value="50x30">50×30 mm</option>
              <option value="40x30">40×30 mm</option>
              <option value="75x130">75×130 mm</option>
              <option value="custom">Custom Size</option>
            </select>
            
            <div id="customSizeInputs" class="hidden flex gap-2 items-center">
              <div class="flex items-center gap-1">
                <label class="text-xs text-slate-600">W:</label>
                <input id="customWidth" type="number" value="60" min="10" max="200" step="1" class="border rounded px-2 py-1 w-16 text-sm" />
                <span class="text-xs text-slate-500">mm</span>
              </div>
              <div class="flex items-center gap-1">
                <label class="text-xs text-slate-600">H:</label>
                <input id="customHeight" type="number" value="20" min="10" max="200" step="1" class="border rounded px-2 py-1 w-16 text-sm" />
                <span class="text-xs text-slate-500">mm</span>
              </div>
              <button id="applyCustomSize" class="text-xs px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600">Apply</button>
            </div>
            
            <div class="mt-3">
              <label class="text-sm text-slate-600 block mb-2">Orientation</label>
              <div class="inline-flex border rounded overflow-hidden" role="group">
                <button type="button" id="orientationPortrait" class="px-3 py-2 hover:bg-slate-100 border-r bg-blue-500 text-white" title="Portrait">
                  <svg width="20" height="24" viewBox="0 0 20 24" class="w-5 h-6">
                    <rect x="2" y="2" width="16" height="20" fill="none" stroke="currentColor" stroke-width="2"/>
                    <text x="10" y="14" text-anchor="middle" font-size="8" fill="currentColor">A</text>
                  </svg>
                </button>
                <button type="button" id="orientationLandscape" class="px-3 py-2 hover:bg-slate-100" title="Landscape">
                  <svg width="24" height="20" viewBox="0 0 24 20" class="w-6 h-5">
                    <rect x="2" y="2" width="20" height="16" fill="none" stroke="currentColor" stroke-width="2"/>
                    <text x="12" y="12" text-anchor="middle" font-size="8" fill="currentColor">A</text>
                  </svg>
                </button>
              </div>
              <div class="text-xs text-slate-500 mt-1">
                <span id="orientationInfo">Portrait: 60×20 mm (W×H)</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Print Settings Section -->
        <div class="bg-white border rounded p-3">
          <h3 class="font-medium mb-2">Print Settings</h3>
          <div class="flex flex-col gap-2">
            <label class="text-sm text-slate-600">Printer</label>
            <input id="printer" class="hidden" />
            <div class="border rounded p-2 bg-slate-50">
              <div class="flex items-center justify-between mb-2">
                <div class="text-sm text-slate-600">Select a printer</div>
                <button id="refreshPrinters" class="text-xs px-2 py-1 bg-slate-200 rounded hover:bg-slate-300">
                  Refresh
                </button>
              </div>
              <div id="printerList" class="flex flex-col gap-1 max-h-44 overflow-auto"></div>
            </div>
            
            <select id="direction" class="hidden border rounded px-2 py-1 w-40">
              <option value="0">0 (front)</option>
              <option value="1" selected>1 (flip 180°)</option>
            </select>
            
            <div class="mt-2 flex items-center gap-3 flex-wrap">
              <div class="inline-flex items-center gap-2">
                <label class="text-sm text-slate-600">Copies</label>
                <input id="copies" type="number" value="1" min="1" step="1" class="border rounded px-2 py-1 w-20" />
              </div>
              <div class="inline-flex items-center gap-2">
                <label class="text-sm text-slate-600">Collation</label>
                <select id="collation" class="border rounded px-2 py-1 w-36">
                  <option value="collated">Collated</option>
                  <option value="uncollated" selected>Uncollated</option>
                </select>
              </div>
              <label class="inline-flex items-center gap-2 text-sm text-slate-600">
                <input id="reverseOrder" type="checkbox" /> Reverse
              </label>
              
              <button id="saveTpl" class="px-3 py-2 bg-slate-200 rounded cursor-pointer hover:bg-slate-300 inline-flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                  <path d="M17 3H5a2 2 0 00-2 2v12h2V7h12V3zm2 4h-4V3l4 4zM5 19h14v2H5v-2zm14-8H5a2 2 0 00-2 2v4h18v-4a2 2 0 00-2-2z" />
                </svg>
                Save
              </button>
              
              <button id="print" class="px-5 py-3 bg-indigo-600 text-white rounded hover:bg-indigo-500 text-base font-semibold shadow inline-flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                  <path d="M6 9V2h12v7H6zm10-2V4H8v3h8zM6 14H4a2 2 0 01-2-2V9a2 2 0 012-2h16a2 2 0 012 2v3a2 2 0 01-2 2h-2v5H6v-5zm2 0v3h8v-3H8z" />
                </svg>
                Print
              </button>
              
              <span id="status" class="text-sm text-slate-600 ml-1"></span>
            </div>
          </div>
        </div>

        <!-- Saved Templates Section -->
        <div class="bg-white border rounded p-3">
          <div class="flex items-center justify-between mb-2">
            <h3 class="font-medium">Saved Templates</h3>
            <div class="flex gap-2">
              <button id="exportTemplates" class="text-xs px-2 py-1 bg-green-500 text-white rounded hover:bg-green-600 inline-flex items-center gap-1" title="Export all templates to JSON file">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-3 h-3">
                  <path d="M12 2l3 3h4a2 2 0 012 2v12a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h4l3-3zm0 2.83L10.83 6H5v12h14V7h-5.83L12 4.83zM12 8v4h3l-4 4-4-4h3V8z"/>
                </svg>
                Export
              </button>
              <label class="text-xs px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 cursor-pointer inline-flex items-center gap-1" title="Import templates from JSON file">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-3 h-3">
                  <path d="M12 2l3 3h4a2 2 0 012 2v12a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h4l3-3zm0 2.83L10.83 6H5v12h14V7h-5.83L12 4.83zM12 16l-4-4h3V8h2v4h3l-4 4z"/>
                </svg>
                <input id="importTemplates" type="file" accept=".json" class="hidden" />
                Import
              </label>
            </div>
          </div>
          <div class="flex flex-wrap gap-2">
            <div id="templates" class="grid grid-cols-1 sm:grid-cols-2 gap-3"></div>
          </div>
        </div>
      </div>

      <!-- Right Panel: Elements, Canvas, Data -->
      <div class="flex-1 flex flex-col gap-4">
        
        <!-- Add Elements Section -->
        <div class="bg-white border rounded p-3">
          <h3 class="font-medium mb-2">Add Elements</h3>
          <div class="flex flex-wrap gap-2">
            <button id="addText" class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300 inline-flex items-center gap-2" title="Add Text">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                <path d="M3 4h18v2H13v14h-2V6H3z" />
              </svg>
              Text
            </button>
            <button id="addBarcode" class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300 inline-flex items-center gap-2" title="Add Barcode">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                <path d="M3 4h2v16H3V4zm3 3h1v10H6V7zm2-3h1v16H8V4zm2 3h2v10h-2V7zm3-3h1v16h-1V4zm2 3h1v10h-1V7zm2-3h2v16h-2V4z" />
              </svg>
              Barcode
            </button>
            <button id="addQR" class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300 inline-flex items-center gap-2" title="Add QR">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                <path d="M3 3h8v8H3V3zm2 2v4h4V5H5zm6 6h2v2h-2v-2zm0-8h2v6h-2V3zm4 0h6v6h-6V3zm2 2v2h2V5h-2zM3 13h6v8H3v-8zm2 2v4h2v-4H5zm8-2h3v3h-3v-3zm3 0h3v2h-1v3h-2v-5zm-3 5h3v3h-3v-3zm5 0h3v3h-3v-3z" />
              </svg>
              QR
            </button>
            <label class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300 cursor-pointer inline-flex items-center gap-2" title="Upload Image">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                <path d="M5 3h14a2 2 0 012 2v14a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2zm0 2v10l3.5-3.5 2.5 2.5L15 9l4 6V5H5z" />
              </svg>
              <input id="imgFile" type="file" accept="image/*" class="hidden" />
              Image
            </label>
            <button id="addLine" class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300 inline-flex items-center gap-2" title="Add Line">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                <rect x="4" y="11" width="16" height="2" />
              </svg>
              Line
            </button>
            <button id="addRect" class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300 inline-flex items-center gap-2" title="Add Rectangle">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                <rect x="5" y="5" width="14" height="10" />
              </svg>
              Rect
            </button>
            <button id="addCircle" class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300 inline-flex items-center gap-2" title="Add Circle">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                <circle cx="12" cy="12" r="5" />
              </svg>
              Circle
            </button>
            <button id="addTriangle" class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300 inline-flex items-center gap-2" title="Add Triangle">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                <path d="M12 5l9 14H3l9-14z" />
              </svg>
              Triangle
            </button>
            <button id="addDateTime" class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300 inline-flex items-center gap-2" title="Add Date/Time">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM12 6c3.31 0 6 2.69 6 6s-2.69 6-6 6-6-2.69-6-6 2.69-6 6-6zm0 2c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm.5 2v2.5l2 1.2-.8 1.3-2.7-1.6V10h1.5z"/>
              </svg>
              Date/Time
            </button>
            <button id="addCounter" class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300 inline-flex items-center gap-2" title="Add Sequential Counter">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                <path d="M2 17h2v.5H3v1h1v.5H2v1h3v-4H2v1zm1-9h1V4H2v1h1v3zm-1 3h1.8L2 13.1v.9h3v-1H3.2L5 10.9V10H2v1zm5-6v2h14V5H7zm0 14h14v-2H7v2zm0-6h14v-2H7v2z"/>
              </svg>
              Counter
            </button>
          </div>
          
          <div class="flex flex-wrap gap-2 mt-2">
            <button id="bringFront" disabled class="px-3 py-2 rounded bg-slate-200 text-slate-400 inline-flex items-center gap-2" title="Bring to front">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                <path d="M7 7h10v10H7V7zm-4 0h2v10H3V7zm16 0h2v10h-2V7zm-8-4h2v2h-2V3zm0 18h2v2h-2v-2z"/>
              </svg>
              To Front
            </button>
            <button id="sendBack" disabled class="px-3 py-2 rounded bg-slate-200 text-slate-400 inline-flex items-center gap-2" title="Send to back">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                <path d="M7 7h10v10H7V7zm-4 0h2v10H3V7zm16 0h2v10h-2V7zM11 3h2v2h-2V3zm0 16h2v2h-2v-2z"/>
              </svg>
              To Back
            </button>
            <button id="undoBtn" class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300 inline-flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                <path d="M12 5V2L7 7l5 5V9c3.31 0 6 2.69 6 6 0 1.12-.31 2.16-.85 3.05l1.46 1.46C19.42 18.35 20 16.75 20 15c0-4.42-3.58-8-8-8z" />
              </svg>
              Undo
            </button>
            <button id="copy" disabled class="px-3 py-2 rounded bg-slate-200 text-slate-400 inline-flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                <path d="M16 1H4a2 2 0 00-2 2v12h2V3h12V1zm3 4H8a2 2 0 00-2 2v16h13a2 2 0 002-2V7a2 2 0 00-2-2zm0 18H8V7h11v16z" />
              </svg>
              Copy
            </button>
            <button id="del" disabled class="px-3 py-2 rounded bg-slate-200 text-slate-400 inline-flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                <path d="M6 7h12l-1 14H7L6 7zm3-3h6l1 2H8l1-2z" />
              </svg>
              Delete
            </button>
            <button id="clearCanvas" class="px-3 py-2 bg-blue-500 text-white rounded cursor-pointer hover:bg-blue-400 inline-flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                <path d="M3 6h18v2H3V6zm2 4h14l-1.5 10h-11L5 10zm5-6h4l1 2H9l1-2z" />
              </svg>
              Clear Label
            </button>
          </div>
        </div>

        <!-- Canvas Area -->
        <div class="bg-white border rounded p-3 flex justify-center items-center">
          <div id="stageWrap" class="inline-block border rounded bg-white">
            <canvas id="stage" width="480" height="160"></canvas>
          </div>
        </div>

        <!-- Text Settings -->
        <div class="bg-white border rounded p-3" id="textControls" style="display: none">
          <h3 class="font-medium mb-2">Text Settings</h3>
          <!-- Hidden state for compatibility with existing logic -->
          <input id="fontFamily" type="hidden" value="Sarabun" />
          <input id="fontSize" type="hidden" value="24" />
          <input id="fontWeight" type="hidden" value="600" />
          <input id="textAlign" type="hidden" value="left" />

          <!-- Toolbar: font family (segmented), size, styles, alignment -->
          <div class="flex items-center gap-3 flex-wrap">
            <!-- Family -->
            <div class="inline-flex border rounded overflow-hidden" role="group" aria-label="Font family">
              <button type="button" class="px-2 py-1 text-sm hover:bg-slate-100 tt-font" data-font="Sarabun" id="ffSarabun">Sarabun</button>
              <button type="button" class="px-2 py-1 text-sm hover:bg-slate-100 tt-font" data-font="Noto Sans Thai" id="ffNoto">Noto</button>
              <button type="button" class="px-2 py-1 text-sm hover:bg-slate-100 tt-font" data-font="system" id="ffSystem">System</button>
            </div>

            <!-- Size stepper -->
            <div class="inline-flex items-center border rounded overflow-hidden" aria-label="Font size">
              <button type="button" id="fsDec" class="px-2 py-1 hover:bg-slate-100">A-</button>
              <div id="fsDisp" class="px-2 py-1 w-10 text-center text-sm">24</div>
              <button type="button" id="fsInc" class="px-2 py-1 hover:bg-slate-100">A+</button>
            </div>

            <!-- Styles -->
            <div class="inline-flex border rounded overflow-hidden" role="group" aria-label="Font styles">
              <button type="button" id="stBold" class="px-2 py-1 hover:bg-slate-100" title="Bold (B)"><strong>B</strong></button>
              <button type="button" id="stItalic" class="px-2 py-1 hover:bg-slate-100 italic" title="Italic (I)">I</button>
              <button type="button" id="stUnderline" class="px-2 py-1 hover:bg-slate-100 underline" title="Underline (U)">U</button>
              <button type="button" id="stStrike" class="px-2 py-1 hover:bg-slate-100 line-through" title="Strikethrough (S)">S</button>
            </div>

            <!-- Alignment -->
            <div class="inline-flex border rounded overflow-hidden" role="group" aria-label="Alignment">
              <button type="button" id="alLeft" class="px-2 py-1 hover:bg-slate-100" title="Align Left">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5"><path d="M3 4h18v2H3V4zm0 4h12v2H3V8zm0 4h18v2H3v-2zm0 4h12v2H3v-2z"/></svg>
              </button>
              <button type="button" id="alCenter" class="px-2 py-1 hover:bg-slate-100" title="Align Center">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5"><path d="M3 4h18v2H3V4zm3 4h12v2H6V8zm-3 4h18v2H3v-2zm3 4h12v2H6v-2z"/></svg>
              </button>
              <button type="button" id="alRight" class="px-2 py-1 hover:bg-slate-100" title="Align Right">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5"><path d="M3 4h18v2H3V4zm6 4h12v2H9V8zM3 12h18v2H3v-2zm6 4h12v2H9v-2z"/></svg>
              </button>
            </div>
          </div>

          <div class="text-xs text-slate-500 mt-2">Text settings are applied automatically when changed.</div>
        </div>

        <!-- Shape Settings -->
        <div class="bg-white border rounded p-3" id="shapeControls" style="display: none">
          <h3 class="font-medium mb-2">Shape Settings</h3>

          <!-- Line Style -->
          <div class="mb-3">
            <label class="text-sm text-slate-600 block mb-2">Line Style</label>
            <div class="inline-flex border rounded overflow-hidden" role="group">
              <button type="button" id="lineStyleSolid" class="px-3 py-2 hover:bg-slate-100 border-r" title="Solid Line">
                <svg width="24" height="12" viewBox="0 0 24 12" class="w-6 h-3">
                  <line x1="2" y1="6" x2="22" y2="6" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
              <button type="button" id="lineStyleDashed" class="px-3 py-2 hover:bg-slate-100 border-r" title="Dashed Line">
                <svg width="24" height="12" viewBox="0 0 24 12" class="w-6 h-3">
                  <line x1="2" y1="6" x2="22" y2="6" stroke="currentColor" stroke-width="2" stroke-dasharray="4,2"/>
                </svg>
              </button>
              <button type="button" id="lineStyleDotted" class="px-3 py-2 hover:bg-slate-100 border-r" title="Dotted Line">
                <svg width="24" height="12" viewBox="0 0 24 12" class="w-6 h-3">
                  <line x1="2" y1="6" x2="22" y2="6" stroke="currentColor" stroke-width="2" stroke-dasharray="1,2"/>
                </svg>
              </button>
              <button type="button" id="lineStyleDashDot" class="px-3 py-2 hover:bg-slate-100" title="Dash-Dot Line">
                <svg width="24" height="12" viewBox="0 0 24 12" class="w-6 h-3">
                  <line x1="2" y1="6" x2="22" y2="6" stroke="currentColor" stroke-width="2" stroke-dasharray="6,2,1,2"/>
                </svg>
              </button>
            </div>
          </div>

          <!-- Line Thickness -->
          <div class="mb-3">
            <label class="text-sm text-slate-600 block mb-2">Line Thickness</label>
            <div class="inline-flex border rounded overflow-hidden" role="group">
              <button type="button" id="thickness1" class="px-3 py-2 hover:bg-slate-100 border-r" title="1px">
                <svg width="24" height="12" viewBox="0 0 24 12" class="w-6 h-3">
                  <line x1="2" y1="6" x2="22" y2="6" stroke="currentColor" stroke-width="1"/>
                </svg>
              </button>
              <button type="button" id="thickness2" class="px-3 py-2 hover:bg-slate-100 border-r" title="2px">
                <svg width="24" height="12" viewBox="0 0 24 12" class="w-6 h-3">
                  <line x1="2" y1="6" x2="22" y2="6" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
              <button type="button" id="thickness3" class="px-3 py-2 hover:bg-slate-100 border-r" title="3px">
                <svg width="24" height="12" viewBox="0 0 24 12" class="w-6 h-3">
                  <line x1="2" y1="6" x2="22" y2="6" stroke="currentColor" stroke-width="3"/>
                </svg>
              </button>
              <button type="button" id="thickness4" class="px-3 py-2 hover:bg-slate-100 border-r" title="4px">
                <svg width="24" height="12" viewBox="0 0 24 12" class="w-6 h-3">
                  <line x1="2" y1="6" x2="22" y2="6" stroke="currentColor" stroke-width="4"/>
                </svg>
              </button>
              <button type="button" id="thickness5" class="px-3 py-2 hover:bg-slate-100" title="5px">
                <svg width="24" height="12" viewBox="0 0 24 12" class="w-6 h-3">
                  <line x1="2" y1="6" x2="22" y2="6" stroke="currentColor" stroke-width="5"/>
                </svg>
              </button>
            </div>
          </div>

          <!-- Fill Style -->
          <div class="mb-3">
            <label class="text-sm text-slate-600 block mb-2">Fill</label>
            <div class="inline-flex border rounded overflow-hidden" role="group">
              <button type="button" id="fillNone" class="px-3 py-2 hover:bg-slate-100 border-r" title="No Fill">
                <svg width="20" height="20" viewBox="0 0 20 20" class="w-5 h-5">
                  <rect x="2" y="2" width="16" height="16" fill="none" stroke="currentColor" stroke-width="1"/>
                  <line x1="2" y1="18" x2="18" y2="2" stroke="red" stroke-width="2"/>
                </svg>
              </button>
              <button type="button" id="fillSolid" class="px-3 py-2 hover:bg-slate-100" title="Solid Fill">
                <svg width="20" height="20" viewBox="0 0 20 20" class="w-5 h-5">
                  <rect x="2" y="2" width="16" height="16" fill="currentColor" stroke="currentColor" stroke-width="1"/>
                </svg>
              </button>
            </div>
          </div>

          <div class="text-xs text-slate-500 mt-2">Click options to apply to selected shape immediately.</div>
        </div>

        <!-- Image Settings -->
        <div class="bg-white border rounded p-3" id="imgControls" style="display: none">
          <h3 class="font-medium mb-2">Image Settings</h3>
          <div class="flex items-center gap-3">
            <label class="text-sm text-slate-600">Threshold</label>
            <input id="imgThreshold" type="range" min="0" max="255" step="1" value="180" class="w-48" />
            <label class="text-sm text-slate-600 inline-flex items-center gap-1">
              <input id="imgDither" type="checkbox" /> Dither
            </label>
          </div>
          <div class="text-xs text-slate-500 mt-1">
            Adjust B/W for selected image
          </div>
        </div>

        <!-- Date/Time Settings -->
        <div class="bg-white border rounded p-3" id="dateTimeControls" style="display: none">
          <h3 class="font-medium mb-2">Date/Time Settings</h3>

          <!-- Type Selection -->
          <div class="mb-3">
            <label class="text-sm text-slate-600 block mb-2">Type</label>
            <div class="inline-flex border rounded overflow-hidden" role="group">
              <button type="button" id="dtTypeDate" class="px-3 py-2 hover:bg-slate-100 border-r bg-blue-500 text-white" title="Date Only">Date</button>
              <button type="button" id="dtTypeTime" class="px-3 py-2 hover:bg-slate-100 border-r" title="Time Only">Time</button>
              <button type="button" id="dtTypeDateTime" class="px-3 py-2 hover:bg-slate-100" title="Date & Time">Date/Time</button>
            </div>
          </div>

          <!-- Date Format -->
          <div class="mb-3" id="dateFormatSection">
            <label class="text-sm text-slate-600 block mb-2">Date Format</label>
            <select id="dateFormat" class="w-full px-2 py-1 border rounded text-sm mb-2">
              <option value="DD/MM/YYYY">DD/MM/YYYY (01/09/2568)</option>
              <option value="DD/MM/YY">DD/MM/YY (01/09/68)</option>
              <option value="YYYY-MM-DD">YYYY-MM-DD (2568-09-01)</option>
              <option value="dddd DD mmmm YYYY">dddd DD mmmm YYYY (จันทร์ 01 กันยายน 2568)</option>
              <option value="dd DD/mmm/YYYY">dd DD/mmm/YYYY (จ. 01/ก.ย./2568)</option>
              <option value="mmmm DD, YYYY">mmmm DD, YYYY (กันยายน 01, 2568)</option>
              <option value="DD-MM-YYYY">DD-MM-YYYY (01-09-2568)</option>
              <option value="DD/MM/YYYY HH:mm:ss">DD/MM/YYYY HH:mm:ss (01/09/2568 13:15:00)</option>
              <option value="YYYY-MM-DD HH:mm:ss">YYYY-MM-DD HH:mm:ss (2568-09-01 13:15:00)</option>
              <option value="custom">Custom Format</option>
            </select>
            <div id="customDateFormat" class="hidden">
              <input type="text" id="customDateInput" placeholder="e.g., DD/mmm/YYYY HH:mm:ss" class="w-full px-2 py-1 border rounded text-sm mb-1">
              <div class="text-xs text-slate-500">
                DD=day(2), MM=month(2), YYYY=year(4), YY=year(2)<br>
                dddd=day name(full), dd=day name(short)<br>
                mmmm=month name(full), mmm=month name(short)<br>
                HH=hour(24), hh=hour(12), mm=minute, ss=second, AM/PM
              </div>
            </div>
          </div>

          <!-- Time Format -->
          <div class="mb-3" id="timeFormatSection" style="display: none">
            <label class="text-sm text-slate-600 block mb-2">Time Format</label>
            <select id="timeFormat" class="w-full px-2 py-1 border rounded text-sm">
              <option value="HH:mm:ss">HH:mm:ss (13:15:00)</option>
              <option value="HH:mm">HH:mm (13:15)</option>
              <option value="hh:mm:ss AM/PM">hh:mm:ss AM/PM (01:15:00 PM)</option>
              <option value="hh:mm AM/PM">hh:mm AM/PM (01:15 PM)</option>
            </select>
          </div>

          <!-- Year Type -->
          <div class="mb-3">
            <label class="text-sm text-slate-600 block mb-2">Year Type</label>
            <div class="inline-flex border rounded overflow-hidden" role="group">
              <button type="button" id="yearTypeCE" class="px-3 py-2 hover:bg-slate-100 border-r" title="Christian Era">C.E.</button>
              <button type="button" id="yearTypeBE" class="px-3 py-2 hover:bg-slate-100 bg-blue-500 text-white" title="Buddhist Era">B.E.</button>
            </div>
          </div>

          <!-- Language -->
          <div class="mb-3">
            <label class="text-sm text-slate-600 block mb-2">Language</label>
            <div class="inline-flex border rounded overflow-hidden" role="group">
              <button type="button" id="langEN" class="px-3 py-2 hover:bg-slate-100 border-r" title="English">English</button>
              <button type="button" id="langTH" class="px-3 py-2 hover:bg-slate-100 bg-blue-500 text-white" title="Thai">ไทย</button>
            </div>
          </div>

          <!-- Update Mode -->
          <div class="mb-3">
            <label class="text-sm text-slate-600 block mb-2">Update Mode</label>
            <div class="space-y-2">
              <label class="inline-flex items-center">
                <input type="radio" name="updateMode" value="current" checked class="mr-2">
                <span class="text-sm">Current (fixed at creation time)</span>
              </label>
              <label class="inline-flex items-center">
                <input type="radio" name="updateMode" value="auto" class="mr-2">
                <span class="text-sm">Auto (updates on each print)</span>
              </label>
            </div>
          </div>

          <div class="text-xs text-slate-500 mt-3">
            Settings are applied automatically when changed.
          </div>
        </div>

        <!-- Counter Settings -->
        <div class="bg-white border rounded p-3" id="counterControls" style="display: none">
          <h3 class="font-medium mb-2">Counter Settings</h3>
          <div class="grid grid-cols-2 gap-3 mb-3">
            <div>
              <label class="text-sm text-slate-600 block mb-1">Start Number</label>
              <input id="counterStart" type="number" min="0" max="99999" value="1" class="w-full px-2 py-1 border rounded text-sm" />
            </div>
            <div>
              <label class="text-sm text-slate-600 block mb-1">Padding (Digits)</label>
              <select id="counterPadding" class="w-full px-2 py-1 border rounded text-sm">
                <option value="1">1 (1, 2, 3...)</option>
                <option value="2">2 (01, 02, 03...)</option>
                <option value="3" selected>3 (001, 002, 003...)</option>
                <option value="4">4 (0001, 0002, 0003...)</option>
                <option value="5">5 (00001, 00002, 00003...)</option>
              </select>
            </div>
          </div>
          <div class="mb-3">
            <label class="text-sm text-slate-600 block mb-2">Counter Mode</label>
            <div class="space-y-2">
              <label class="inline-flex items-center">
                <input type="radio" name="counterMode" value="data" checked class="mr-2">
                <span class="text-sm">By Data Records</span>
              </label>
              <label class="inline-flex items-center">
                <input type="radio" name="counterMode" value="print" class="mr-2">
                <span class="text-sm">By Print Sequence</span>
              </label>
            </div>
            <div class="text-xs text-slate-500 mt-1">
              <div><strong>By Data Records:</strong> 3 data, 2 copies → 10,10, 11,11, 12,12</div>
              <div><strong>By Print Sequence:</strong> 3 data, 2 copies → 10, 11, 12, 13, 14, 15 (ignores collation)</div>
            </div>
          </div>
          <div class="text-xs text-slate-500 mt-3">
            Counter increases by 1 for each record in Data JSON.<br>
            Settings are applied automatically when changed.
          </div>
        </div>

        <!-- Data Section -->
        <div class="bg-white border rounded p-3">
          <h3 class="font-medium mb-2">Data (JSON Array)</h3>
          <div class="flex flex-col gap-2">
            <textarea id="dataJSON" rows="6" class="border rounded p-2 font-mono text-xs" placeholder='[
  { "uic": "XX010101", "id_card": "1234123412340" },
  { "uic": "YY010101", "id_card": "1234123412341" }
]'></textarea>
            <div class="flex items-center gap-3 flex-wrap">
              <label class="text-sm text-slate-600">Preview row</label>
              <input id="dataIndex" type="number" value="1" min="1" step="1" class="border rounded px-2 py-1 w-20" />
              <button id="previewData" class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300">
                Apply Preview
              </button>
              <span id="dataStatus" class="text-xs text-slate-500"></span>
            </div>
          </div>
        </div>

        <!-- Bindings Section -->
        <div class="bg-white border rounded p-3">
          <h3 class="font-medium mb-2">Bindings (All Elements)</h3>
          <div id="bindingsList" class="flex flex-col gap-2 max-h-56 overflow-auto"></div>
          <div class="text-xs text-slate-500 mt-1">
            Tip: Paste JSON, click Apply Preview to test bindings.
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- JavaScript Modules - Load after libraries are ready -->
  <script>
    // Wait for libraries to load before initializing modules
    function waitForLibraries() {
      return new Promise((resolve) => {
        const checkLibraries = () => {
          if (typeof fabric !== 'undefined' && typeof QRCode !== 'undefined') {
            resolve();
          } else {
            setTimeout(checkLibraries, 50);
          }
        };
        checkLibraries();
      });
    }

    // Load the main application module after libraries are ready
    waitForLibraries().then(() => {
      const script = document.createElement('script');
      script.type = 'module';
      script.src = './js/app.js';
      document.head.appendChild(script);
    });
  </script>
</body>
</html>
